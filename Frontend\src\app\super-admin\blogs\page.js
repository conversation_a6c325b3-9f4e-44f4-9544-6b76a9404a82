"use client";

import React, { useEffect, useState } from "react";
import { Container, Row, Col, Form, Button } from "react-bootstrap";
import { useFormik } from "formik";
import DashboardLayout from "@/Layouts/DashboardLayout";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import toast from "react-hot-toast";
import { deleteRequest, get, post } from "@/utils/apiUtils";
import { ArticleSchema } from "@/validations/schema";
import JoditEditorComponent from "@/Components/UI/JoditEditorComponent";
import ListingTable from "@/Components/UI/ListingTable";
import "@/css/dashboard/StrategyManager.scss";
import dynamic from "next/dynamic";
import CustomPagination from "@/Components/UI/CustomPagination";
import Head from "next/head";

const Select = dynamic(() => import("react-select"), { ssr: false });

const BlogForm = () => {
  const [meta, setMeta] = useState(null);
  const [newPage, setNewPage] = useState(1);
  const [fetchCategories, setFetchCategories] = useState([]);
  const [listingBlogArticles, setListingBlogArticles] = useState([]);
  const [allBlogArticles, setAllBlogArticles] = useState([]);
  const [editingBlog, setEditingBlog] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoadingAllData, setIsLoadingAllData] = useState(false);
  const [filteredAndSortedArticles, setFilteredAndSortedArticles] = useState([]);
  // Initialize sort state with title field and ascending order
  const [sortField, setSortField] = useState("title");
  const [sortOrder, setSortOrder] = useState("asc");

  // Pagination settings
  const ITEMS_PER_PAGE = 10;

  useEffect(() => {
    const listingCategories = async () => {
      try {
        const response = await get("/super-admin/articles/create");
        setFetchCategories(Array.isArray(response?.data) ? response.data : []);
      } catch (error) {
        console.log(error);
      }
    };

    listingCategories();
  }, []);


  // Function to fetch ALL blog articles from all pages
  const fetchAllBlogArticles = async () => {
    setIsLoadingAllData(true);
    try {
      let allArticles = [];
      let currentPage = 1;
      let hasMorePages = true;

      while (hasMorePages) {
        const response = await get("super-admin/articles/list/blog", { page: currentPage });
        console.log(response);
        const articles = response?.data || [];
        const pagination = response?.pagination || {};

        allArticles = [...allArticles, ...articles];

        // Check if there are more pages
        hasMorePages = pagination.next_page_url !== null;
        currentPage++;
      }

      // Sort articles alphabetically by title before setting them
      const sortedArticles = allArticles.sort((a, b) =>
        (a.title || '').localeCompare(b.title || '')
      );

      setAllBlogArticles(sortedArticles);
      return allArticles;
    } catch (error) {
      console.error("Error fetching all blog articles:", error);
      return [];
    } finally {
      setIsLoadingAllData(false);
    }
  };

  // Helper function to get nested property value
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => current?.[key], obj) || '';
  };

  // Function to apply search and sorting to all articles
  const applySearchAndSort = (articles, searchTerm, field = "title", order = "asc") => {
    let filtered = [...articles];

    // Apply search filter - search ONLY in title field with prioritization
    if (searchTerm.trim()) {
      const lowerSearchTerm = searchTerm.toLowerCase();

      // Separate articles into two groups: those starting with search term and those containing it elsewhere
      const startsWithTerm = [];
      const containsTerm = [];

      articles.forEach(article => {
        const title = article.title?.toLowerCase() || '';
        if (title.startsWith(lowerSearchTerm)) {
          startsWithTerm.push(article);
        } else if (title.includes(lowerSearchTerm)) {
          containsTerm.push(article);
        }
      });

      // Sort each group separately to maintain priority
      const sortFunction = (a, b) => {
        const aValue = getNestedValue(a, field);
        const bValue = getNestedValue(b, field);
        return order === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      };

      startsWithTerm.sort(sortFunction);
      containsTerm.sort(sortFunction);

      // Combine results with priority: starts with term first, then contains term
      filtered = [...startsWithTerm, ...containsTerm];
    } else {
      // Apply normal sorting when no search term
      filtered.sort((a, b) => {
        const aValue = getNestedValue(a, field);
        const bValue = getNestedValue(b, field);
        return order === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      });
    }

    return filtered;
  };

  // Function to paginate the filtered and sorted results
  const paginateResults = (articles, page, itemsPerPage) => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedArticles = articles.slice(startIndex, endIndex);

    const totalPages = Math.ceil(articles.length / itemsPerPage);

    return {
      data: paginatedArticles,
      pagination: {
        current_page: page,
        per_page: itemsPerPage,
        total: totalPages,
        count: paginatedArticles.length,
        next_page_url: page < totalPages ? `page=${page + 1}` : null,
        prev_page_url: page > 1 ? `page=${page - 1}` : null,
        total_records: articles.length
      }
    };
  };

  // Load all data on component mount
  useEffect(() => {
    fetchAllBlogArticles();
  }, []);

  // Apply search and sorting whenever allBlogArticles, searchTerm, or sort parameters change
  useEffect(() => {
    if (allBlogArticles.length > 0) {
      const filtered = applySearchAndSort(allBlogArticles, searchTerm, sortField, sortOrder);
      setFilteredAndSortedArticles(filtered);

      // Reset to page 1 when search term or sort changes
      if (searchTerm !== '' || sortField) {
        setNewPage(1);
      }
    }
  }, [allBlogArticles, searchTerm, sortField, sortOrder]);

  // Update displayed articles and pagination when page or filtered results change
  useEffect(() => {
    if (filteredAndSortedArticles.length > 0) {
      const result = paginateResults(filteredAndSortedArticles, newPage, ITEMS_PER_PAGE);
      setListingBlogArticles(result.data);
      setMeta(result.pagination);
    } else if (filteredAndSortedArticles.length === 0 && allBlogArticles.length > 0) {
      // No search results
      setListingBlogArticles([]);
      setMeta({
        current_page: 1,
        per_page: ITEMS_PER_PAGE,
        total: 0,
        count: 0,
        next_page_url: null,
        prev_page_url: null,
        total_records: 0
      });
    }
  }, [filteredAndSortedArticles, newPage]);

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleSort = (field) => {
    setSortOrder(sortField === field ? (sortOrder === "asc" ? "desc" : "asc") : "asc");
    setSortField(field);
  };

  const handlePrimaryCategoryChange = (event) => {
    const selectedPrimaryCategory = event.target.value;
    formik.setFieldValue("primary_category_id", selectedPrimaryCategory);

    if (selectedPrimaryCategory) {
      const updatedSecondaryCategories = formik.values.secondary_categories.filter(
        (id) => String(id) !== String(selectedPrimaryCategory)
      );
      formik.setFieldValue("secondary_categories", updatedSecondaryCategories);
    }
  };

  const getFilteredSecondaryOptions = () => {
    return (fetchCategories || [])
      .filter((cat) => String(cat.id) !== String(formik.values.primary_category_id))
      .map((cat) => ({
        value: cat.id,
        label: cat.title
      }));
  };

  const getSelectedSecondaryValues = () => {
    return formik.values.secondary_categories
      .map((categoryId) => {
        const category = fetchCategories.find((cat) => String(cat.id) === String(categoryId));
        if (!category) return null;
        return { value: category.id, label: category.title };
      })
      .filter(Boolean);
  };

  const handleSecondaryCategoryChange = (selectedOptions) => {
    const selectedIds = selectedOptions.map((option) => option.value);
    formik.setFieldValue("secondary_categories", selectedIds);
  };

  const handleEdit = (blogArticles) => {
    setEditingBlog(blogArticles);

    formik.setValues({
      primary_category_id: blogArticles?.primary_category?.id || "",
      title: blogArticles?.title || "",
      summary: blogArticles?.summary || "",
      content: blogArticles?.content || "",
      secondary_categories: blogArticles?.secondary_categories?.map(cat => String(cat.id)) || [],
      feature_image: blogArticles?.feature_image || null,
    });

  };

  const formik = useFormik({
    initialValues: {
      // url_path: "",
      secondary_categories: [],
      primary_category_id: "",
      title: "",
      feature_image: "",
      content: "",
      summary: ""
    },
    validationSchema: ArticleSchema,
    validateOnChange: true,
    validateOnBlur: true,
    onSubmit: async (values) => {
      setIsSubmitting(true); // disable the button

      try {
        const formData = new FormData();

        if (values.primary_category_id) {
          formData.append("primary_category_id", values.primary_category_id);
        }

        values.secondary_categories.forEach((id) => {
          formData.append("secondary_categories[]", id);
        });

        formData.append("title", values.title);
        formData.append("content", values.content);
        formData.append("summary", values.summary);
        formData.append("feature_image", values.feature_image);
        formData.append("type", 'blog');


        let response;
        if (editingBlog) {
          formData.append("_method", "PUT");
          response = await post(`/super-admin/articles/${editingBlog?.slug}`, formData);
        } else {
          response = await post("/super-admin/articles/store", formData);
        }

        if (response?.message) {
          toast.success(response.message);
        }

        formik.resetForm();
        setEditingBlog(null);
        fetchAllBlogArticles();

      } catch (error) {
        console.error("Error submitting form:", error);
        toast.error(error?.response?.data?.message ?? "Something went wrong. Please try again.");
      } finally {
        setIsSubmitting(false); // re-enable the button
      }
      
    }
  });



  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this item?")) return;

    try {
      const response = await deleteRequest(`/super-admin/articles/${id}`);
      if (response?.success) {
        toast.success("Blog deleted successfully");
        // Refresh all data after deletion
        fetchAllBlogArticles();
      } else {
        toast.error("Error deleting blog");
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Error deleting");
    }
  };

  const handleDataFromChild = (childData) => {
    setNewPage(childData);
  };

  // Replace with real data from props or API if needed
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://www.tradereply.com/blog/page/${newPage}`
    },
    "headline": "Sample Blog Title",
    "description": "Short blog summary goes here.",
    "image": "https://cdn.tradereply.com/blog/sample.jpg",
    "author": {
      "@type": "Organization",
      "name": "TradeReply"
    },
    "publisher": {
      "@type": "Organization",
      "name": "TradeReply",
      "logo": {
        "@type": "ImageObject",
        "url": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png"
      }
    },
    "datePublished": "2025-07-10T12:00:00Z",
    "dateModified": "2025-07-10T12:00:00Z",
    "articleBody": "This is a short summary (500–600 characters) of the blog content...",
    "keywords": "trading, finance, stocks, crypto, investing"
  };

  return (
    <DashboardLayout>
      <Head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />
      </Head>
      <Container>
        <AdminHeading heading="Blog" className="pt-4 pb-6" centered />
        <Form onSubmit={formik.handleSubmit}>

          <Row>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Primary Category</Form.Label>
                <Form.Select
                  name="primary_category_id"
                  value={formik.values.primary_category_id}
                  onChange={handlePrimaryCategoryChange}
                >
                  <option value="">Select a category</option>
                  {fetchCategories?.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.title}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group>
                <Form.Label>Secondary Categories</Form.Label>
                <Select
                  isMulti
                  name="secondary_categories"
                  options={getFilteredSecondaryOptions()} // ✅ Uses function to filter options
                  value={getSelectedSecondaryValues()} // ✅ Uses function to format selected values
                  onChange={handleSecondaryCategoryChange} // ✅ Uses function to update values
                  isDisabled={!formik.values.primary_category_id} // ✅ Disable if no primary is selected
                  classNamePrefix="select"
                  styles={{
                    control: (provided) => ({
                      ...provided,
                      backgroundColor: "white",
                      color: "black",
                      borderColor: "#ccc",
                    }),
                    menu: (provided) => ({
                      ...provided,
                      backgroundColor: "white",
                      color: "black",
                    }),
                    option: (provided, state) => ({
                      ...provided,
                      backgroundColor: state.isSelected ? "#f0f0f0" : "white",
                      color: "black",
                      "&:hover": {
                        backgroundColor: "#e6e6e6",
                      },
                    }),
                  }}
                />
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Page Title</Form.Label>
                <Form.Control
                  type="text"
                  name="title"
                  value={formik.values.title}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.title && formik.errors.title}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.title}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>

            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Header Image Filename (1260x552px) - Serves CDN /blog/featured/</Form.Label>
                <Form.Control
                  type="text"
                  name="feature_image"
                  value={formik.values.feature_image}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.feature_image && formik.errors.feature_image}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.feature_image}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Schema: articleBody(500-600 char summary)</Form.Label>
                <Form.Control
                  type="text"
                  name="articleBody"
                  value={formik.values.articleBody}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.articleBody && formik.errors.articleBody}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.title}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Schema: keywords(5-8 comma-separated SEO keywords)</Form.Label>
                <Form.Control
                  type="text"
                  name="keywords"
                  value={formik.values.keywords}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.keywords && formik.errors.keywords}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.keywords}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Page Summary</Form.Label>
                <Form.Control
                  as="textarea"
                  name="summary"
                  maxLength={250}
                  value={formik.values.summary}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.summary && formik.errors.summary}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.summary}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Body Text</Form.Label>
                <div className="editor-container">
                  <JoditEditorComponent
                    value={formik.values.content}
                    onChange={(newValue) =>
                      formik.setFieldValue("content", newValue)
                    }
                  />
                </div>
                {formik.touched.content && formik.errors.content && (
                  <div className="text-white">{formik.errors.content}</div>
                )}
              </Form.Group>
            </Col>
          </Row>
          {/* Submit Button */}
          <Button type="submit" className="mt-4" disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : editingBlog ? "Update" : "Submit"}
          </Button>
        </Form>

        {/* Search Bar */}
        <div className="mt-4 mb-3">
          <Form.Group>
            <Form.Control
              type="text"
              placeholder="Search by name..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-100"
              disabled={isLoadingAllData}
            />
          </Form.Group>
          {/* Search Results Count with white text */}
          {!isLoadingAllData && filteredAndSortedArticles.length > 0 && searchTerm && (
            <div className="mt-2" style={{ color: '#fff' }}>
              Found {filteredAndSortedArticles.length} result{filteredAndSortedArticles.length !== 1 ? 's' : ''} for "{searchTerm}"
            </div>
          )}
          {!isLoadingAllData && filteredAndSortedArticles.length === 0 && searchTerm && (
            <div className="mt-2" style={{ color: '#fff' }}>
              No results found for "{searchTerm}"
            </div>
          )}
        </div>

        {/* Loading Indicator */}
        {isLoadingAllData && (
          <div className="text-center py-4">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading all blog articles...</span>
            </div>
            <div className="mt-2">Loading all blog articles...</div>
          </div>
        )}

        {/* Articles Table */}
        {!isLoadingAllData && (
          <div className="trade_manager_entrylist">
            <ListingTable
              array={listingBlogArticles}
              onUpdate={handleEdit}
              onDelete={handleDelete}
              onSort={handleSort}
              sortField={sortField}
              sortOrder={sortOrder}
            />
          </div>
        )}

        {/* Pagination */}
        {!isLoadingAllData && meta && meta.total > 1 && (
          <div className="d-flex justify-content-end mt-3">
            <CustomPagination
              useLinks={false}
              links={meta}
              onDataSend={handleDataFromChild}
              pageUrl={"super-admin/blogs/"}
            />
          </div>
        )}
      </Container>
    </DashboardLayout>
  );
};

export default BlogForm;

