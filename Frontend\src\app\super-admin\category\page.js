"use client";

import { Container, Form } from "react-bootstrap";
import { PlusIcon } from "@/assets/svgIcons/SvgIcon";
import CommonButton from "@/Components/UI/CommonButton";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import "@/css/dashboard/StrategyManager.scss";
import "@/css/dashboard/StrategyBuilder.scss";
import DashboardLayout from "@/Layouts/DashboardLayout";
import { useEffect, useState } from "react";
import { deleteRequest, get, post, put } from "@/utils/apiUtils";
import toast from "react-hot-toast";
import ListingTable from "@/Components/UI/ListingTable";
import CustomPagination from "@/Components/UI/CustomPagination";

const CategoryForm = () => {
  const [createCategories, setCreateCategories] = useState({
    title: "",
    content: "",
  });
  const [listingCategories, setListingCategories] = useState([]);
  const [allCategories, setAllCategories] = useState([]);
  const [editCategory, setEditCategory] = useState(null);
  const [meta, setMeta] = useState(null);
  const [newPage, setNewPage] = useState(1);
  const [isEdit, setIsEdit] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoadingAllData, setIsLoadingAllData] = useState(false);
  const [filteredAndSortedCategories, setFilteredAndSortedCategories] = useState([]);
  // Initialize sort state with title field and ascending order
  const [sortField, setSortField] = useState("title");
  const [sortOrder, setSortOrder] = useState("asc");

  // Pagination settings
  const ITEMS_PER_PAGE = 10;

  // Function to fetch ALL categories from all pages
  const fetchAllCategories = async () => {
    setIsLoadingAllData(true);
    try {
      let allCategoriesData = [];
      let currentPage = 1;
      let hasMorePages = true;

      while (hasMorePages) {
        const response = await get("/super-admin/category/categories-list", { page: currentPage });
        console.log(response)
        const categories = response?.data || [];
        const pagination = response?.pagination || {};

        allCategoriesData = [...allCategoriesData, ...categories];

        // Check if there are more pages
        hasMorePages = pagination.next_page_url !== null;
        currentPage++;
      }

      // Sort categories alphabetically by title before setting them
      const sortedCategories = allCategoriesData.sort((a, b) =>
        (a.title || '').localeCompare(b.title || '')
      );

      setAllCategories(sortedCategories);
      return allCategoriesData;
    } catch (error) {
      console.error("Error fetching all categories:", error);
      return [];
    } finally {
      setIsLoadingAllData(false);
    }
  };

  // Helper function to get nested property value
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => current?.[key], obj) || '';
  };

  // Function to apply search and sorting to all categories
  const applySearchAndSort = (categories, searchTerm, field = "title", order = "asc") => {
    let filtered = [...categories];

    // Apply search filter - search ONLY in title field with prioritization
    if (searchTerm.trim()) {
      const lowerSearchTerm = searchTerm.toLowerCase();

      // Separate categories into two groups: those starting with search term and those containing it elsewhere
      const startsWithTerm = [];
      const containsTerm = [];

      categories.forEach(category => {
        const title = category.title?.toLowerCase() || '';
        if (title.startsWith(lowerSearchTerm)) {
          startsWithTerm.push(category);
        } else if (title.includes(lowerSearchTerm)) {
          containsTerm.push(category);
        }
      });

      // Sort each group separately to maintain priority
      const sortFunction = (a, b) => {
        const aValue = getNestedValue(a, field);
        const bValue = getNestedValue(b, field);
        return order === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      };

      startsWithTerm.sort(sortFunction);
      containsTerm.sort(sortFunction);

      // Combine results with priority: starts with term first, then contains term
      filtered = [...startsWithTerm, ...containsTerm];
    } else {
      // Apply normal sorting when no search term
      filtered.sort((a, b) => {
        const aValue = getNestedValue(a, field);
        const bValue = getNestedValue(b, field);
        return order === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      });
    }

    return filtered;
  };

  // Function to paginate the filtered and sorted results
  const paginateResults = (categories, page, itemsPerPage) => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedCategories = categories.slice(startIndex, endIndex);

    const totalPages = Math.ceil(categories.length / itemsPerPage);

    return {
      data: paginatedCategories,
      pagination: {
        current_page: page,
        per_page: itemsPerPage,
        total: totalPages,
        count: paginatedCategories.length,
        next_page_url: page < totalPages ? `page=${page + 1}` : null,
        prev_page_url: page > 1 ? `page=${page - 1}` : null,
        total_records: categories.length
      }
    };
  };

  // Load all data on component mount
  useEffect(() => {
    fetchAllCategories();
  }, []);

  // Apply search and sorting whenever allCategories, searchTerm, or sort parameters change
  useEffect(() => {
    if (allCategories.length > 0) {
      const filtered = applySearchAndSort(allCategories, searchTerm, sortField, sortOrder);
      setFilteredAndSortedCategories(filtered);

      // Reset to page 1 when search term or sort changes
      if (searchTerm !== '' || sortField) {
        setNewPage(1);
      }
    }
  }, [allCategories, searchTerm, sortField, sortOrder]);

  // Update displayed categories and pagination when page or filtered results change
  useEffect(() => {
    if (filteredAndSortedCategories.length > 0) {
      const result = paginateResults(filteredAndSortedCategories, newPage, ITEMS_PER_PAGE);
      setListingCategories(result.data);
      setMeta(result.pagination);
    } else if (filteredAndSortedCategories.length === 0 && allCategories.length > 0) {
      // No search results
      setListingCategories([]);
      setMeta({
        current_page: 1,
        per_page: ITEMS_PER_PAGE,
        total: 0,
        count: 0,
        next_page_url: null,
        prev_page_url: null,
        total_records: 0
      });
    }
  }, [filteredAndSortedCategories, newPage]);

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleSort = (field) => {
    setSortOrder(sortField === field ? (sortOrder === "asc" ? "desc" : "asc") : "asc");
    setSortField(field);
  };

  const handleChange = (e) => {
    setCreateCategories({
      ...createCategories,
      [e.target.name]: e.target.value,
    });
  };

  const createCategory = async () => {
    try {
      if (isEdit && editCategory) {
        const response = await put(
          `/super-admin/category/${editCategory.slug}`,
          createCategories
        );

        if (response?.success) {
          toast.success("Category successfully updated!");
          // Refresh all data after update
          fetchAllCategories();
          resetForm();
        }
      } else {
        const response = await post("/super-admin/category/store", createCategories);

        if (response?.success) {
          toast.success("Category successfully created!");
          // Refresh all data after creation
          fetchAllCategories();
          resetForm();
        }
      }
    } catch (error) {
      console.error(error);
      toast.error(error?.response?.error || "Something went wrong");
    }
  };

  const resetForm = () => {
    setCreateCategories({ title: "", content: "" });
    setEditCategory(null);
    setIsEdit(false);
  };

  const handleDataFromChild = (childData) => {
    setNewPage(childData);
  };

  const handleEdit = (category) => {
    setEditCategory(category);
    setCreateCategories({ title: category.title, content: category.content });
    setIsEdit(true);
  };

  const handleDelete = async (id, slug) => {
    if (!window.confirm("Are you sure you want to delete this item?")) return;

    try {
      const response = await deleteRequest(`/super-admin/category/${slug}`);
      if (response?.success) {
        toast.success(response?.message);
        // Refresh all data after deletion
        fetchAllCategories();
      } else {
        toast.error("Error deleting category");
      }
    } catch (error) {
      console.error("Error in Deleting Category", error);
      toast.error(error?.response?.data?.message || "Error deleting");
    }
  };

  return (
    <DashboardLayout>
      <div className="trade_manager">
        <Container>
          {/* <CommonHead /> */}
          <AdminHeading heading="Category" className="pt-4 pb-6" centered />

          <div className="strategy_builder_inputs mb-4">
            <div className="customInput">
              <label htmlFor="category_name" className="form-label">
                Category Name
              </label>
              <input
                type="text"
                name="title"
                value={createCategories.title}
                onChange={handleChange}
                placeholder="e.g., Earnings Breakout"
                className="form-control"
              />
            </div>
            <div className="customInput">
              <label htmlFor="category_description" className="form-label">
                Category Description
              </label>
              <textarea
                name="content"
                value={createCategories.content}
                onChange={handleChange}
                placeholder="e.g., Trades triggered by a price surge following an earnings report."
                className="form-control"
                rows="3"  // Adjust the initial height
                style={{ resize: "vertical" }}  // Allow resizing vertically
              />

            </div>
          </div>

          <div className="trade_manager_btns">
            <CommonButton
              onClick={createCategory}
              onlyIcon={<PlusIcon />}
              title={isEdit ? "Update Category" : "Create Category"}
              className="w-100 me-2"
            />
          </div>

          {/* Search Bar */}
          <div className="mt-4 mb-3">
            <Form.Group>
              <Form.Control
                type="text"
                placeholder="Search by name..."
                value={searchTerm}
                onChange={handleSearch}
                className="w-100"
                disabled={isLoadingAllData}
              />
            </Form.Group>
            {/* Search Results Count with white text */}
            {!isLoadingAllData && filteredAndSortedCategories.length > 0 && searchTerm && (
              <div className="mt-2" style={{ color: '#fff' }}>
                Found {filteredAndSortedCategories.length} result{filteredAndSortedCategories.length !== 1 ? 's' : ''} for "{searchTerm}"
              </div>
            )}
            {!isLoadingAllData && filteredAndSortedCategories.length === 0 && searchTerm && (
              <div className="mt-2" style={{ color: '#fff' }}>
                No results found for "{searchTerm}"
              </div>
            )}
          </div>

          {/* Loading Indicator */}
          {isLoadingAllData && (
            <div className="text-center py-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading all categories...</span>
              </div>
              <div className="mt-2">Loading all categories...</div>
            </div>
          )}

          {/* Categories Table */}
          {!isLoadingAllData && (
            <div className="trade_manager_entrylist">
              <ListingTable
                array={listingCategories}
                onUpdate={handleEdit}
                onDelete={handleDelete}
                categoryFlag={true}
                onSort={handleSort}
                sortField={sortField}
                sortOrder={sortOrder}
              />
            </div>
          )}

          {/* Pagination */}
          {!isLoadingAllData && meta && meta.total > 1 && (
            <div className="d-flex justify-content-end mt-3">
              <CustomPagination
                useLinks={false}
                links={meta}
                onDataSend={handleDataFromChild}
                pageUrl={"super-admin/category"}
              />
            </div>
          )}
        </Container>
      </div>
    </DashboardLayout>
  );
};

export default CategoryForm;
