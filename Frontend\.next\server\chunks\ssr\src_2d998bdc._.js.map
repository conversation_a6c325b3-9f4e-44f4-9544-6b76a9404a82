{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/NavLink.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\nexport default function NavLink({ className = \"\", children, href, ...props }) {\r\n  const pathname = usePathname();\r\n\r\n  const isActive = pathname === href;\r\n  return (\r\n    <Link\r\n      href={href}\r\n      {...props}\r\n      className={`${className} ${isActive ? \"active\" : \"\"}`}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,QAAQ,EAAE,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO;IAC1E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,aAAa;IAC9B,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACL,GAAG,KAAK;QACT,WAAW,GAAG,UAAU,CAAC,EAAE,WAAW,WAAW,IAAI;kBAEpD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/assets/svgIcons/SvgIcon.js"], "sourcesContent": ["export const EyeIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"12\"\r\n      viewBox=\"0 0 18 12\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 9.70508C9.9375 9.70508 10.7345 9.37708 11.391 8.72108C12.047 8.06458 12.375 7.26758 12.375 6.33008C12.375 5.39258 12.047 4.59558 11.391 3.93908C10.7345 3.28308 9.9375 2.95508 9 2.95508C8.0625 2.95508 7.2655 3.28308 6.609 3.93908C5.953 4.59558 5.625 5.39258 5.625 6.33008C5.625 7.26758 5.953 8.06458 6.609 8.72108C7.2655 9.37708 8.0625 9.70508 9 9.70508ZM9 8.35508C8.4375 8.35508 7.9595 8.15808 7.566 7.76408C7.172 7.37058 6.975 6.89258 6.975 6.33008C6.975 5.76758 7.172 5.28933 7.566 4.89533C7.9595 4.50183 8.4375 4.30508 9 4.30508C9.5625 4.30508 10.0408 4.50183 10.4347 4.89533C10.8282 5.28933 11.025 5.76758 11.025 6.33008C11.025 6.89258 10.8282 7.37058 10.4347 7.76408C10.0408 8.15808 9.5625 8.35508 9 8.35508ZM9 11.9551C7.175 11.9551 5.5125 11.4456 4.0125 10.4266C2.5125 9.40808 1.425 8.04258 0.75 6.33008C1.425 4.61758 2.5125 3.25183 4.0125 2.23283C5.5125 1.21433 7.175 0.705078 9 0.705078C10.825 0.705078 12.4875 1.21433 13.9875 2.23283C15.4875 3.25183 16.575 4.61758 17.25 6.33008C16.575 8.04258 15.4875 9.40808 13.9875 10.4266C12.4875 11.4456 10.825 11.9551 9 11.9551ZM9 10.4551C10.4125 10.4551 11.7095 10.0831 12.891 9.33908C14.072 8.59558 14.975 7.59258 15.6 6.33008C14.975 5.06758 14.072 4.06433 12.891 3.32033C11.7095 2.57683 10.4125 2.20508 9 2.20508C7.5875 2.20508 6.2905 2.57683 5.109 3.32033C3.928 4.06433 3.025 5.06758 2.4 6.33008C3.025 7.59258 3.928 8.59558 5.109 9.33908C6.2905 10.0831 7.5875 10.4551 9 10.4551Z\"\r\n        fill=\"#101014\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const CloseEye = () => (\r\n  <svg\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    width=\"22.016\"\r\n    height=\"17.613\"\r\n    viewBox=\"0 0 22.016 17.613\"\r\n  >\r\n    <path\r\n      id=\"Icon_awesome-eye-slash\"\r\n      data-name=\"Icon awesome-eye-slash\"\r\n      d=\"M11.008,13.76A4.935,4.935,0,0,1,6.092,9.181L2.484,6.392A11.465,11.465,0,0,0,1.221,8.3a1.113,1.113,0,0,0,0,1,11.033,11.033,0,0,0,9.787,6.1,10.685,10.685,0,0,0,2.679-.36L11.9,13.67a4.958,4.958,0,0,1-.894.09Zm10.8,2L18,12.819A11.4,11.4,0,0,0,20.8,9.308a1.113,1.113,0,0,0,0-1,11.033,11.033,0,0,0-9.787-6.1A10.6,10.6,0,0,0,5.94,3.5L1.564.116a.55.55,0,0,0-.773.1l-.675.869a.55.55,0,0,0,.1.772L20.452,17.5a.55.55,0,0,0,.773-.1l.676-.869a.55.55,0,0,0-.1-.772Zm-6.32-4.885L14.131,9.829a3.26,3.26,0,0,0-3.994-4.194,1.639,1.639,0,0,1,.32.97,1.6,1.6,0,0,1-.053.344L7.872,4.992a4.9,4.9,0,0,1,3.136-1.139,4.951,4.951,0,0,1,4.954,4.954,4.836,4.836,0,0,1-.478,2.068Z\"\r\n      transform=\"translate(0 0)\"\r\n      fill=\"#fff\"\r\n    />\r\n  </svg>\r\n);\r\nexport const ThemeIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"32\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 32 32\"\r\n    >\r\n      <g\r\n        id=\"Group_175598\"\r\n        data-name=\"Group 175598\"\r\n        transform=\"translate(-1847 -26)\"\r\n      >\r\n        <g\r\n          id=\"Group_175597\"\r\n          data-name=\"Group 175597\"\r\n          transform=\"translate(1842.007 28.533)\"\r\n        >\r\n          <rect\r\n            id=\"Rectangle_13566\"\r\n            data-name=\"Rectangle 13566\"\r\n            width=\"32\"\r\n            height=\"32\"\r\n            rx=\"16\"\r\n            transform=\"translate(4.993 -2.533)\"\r\n            fill=\"#f45126\"\r\n          />\r\n          <g\r\n            id=\"Group_175601\"\r\n            data-name=\"Group 175601\"\r\n            transform=\"translate(6.923 -0.604)\"\r\n          >\r\n            <path\r\n              id=\"Path_113806\"\r\n              data-name=\"Path 113806\"\r\n              d=\"M41.464,28.649a4.427,4.427,0,0,1-.409.578.185.185,0,1,0,.283.237,4.839,4.839,0,0,0,.444-.625.185.185,0,0,0-.317-.189Zm.521-1.312a4.5,4.5,0,0,1-.208.677.185.185,0,0,0,.343.136,4.837,4.837,0,0,0,.225-.733.184.184,0,1,0-.36-.08Zm.086-1.409a4.537,4.537,0,0,1,.012.708.184.184,0,1,0,.368.023,4.831,4.831,0,0,0-.013-.766.185.185,0,1,0-.367.035Zm-.355-1.366a4.469,4.469,0,0,1,.231.669.185.185,0,0,0,.357-.093,4.833,4.833,0,0,0-.251-.724.184.184,0,1,0-.338.148Zm-.764-1.187a4.508,4.508,0,0,1,.43.563.185.185,0,1,0,.31-.2,4.786,4.786,0,0,0-.465-.609.185.185,0,1,0-.275.246Z\"\r\n              transform=\"translate(-23.867 -12.612)\"\r\n              fill=\"#fff\"\r\n              fillRule=\"evenodd\"\r\n            />\r\n            <path\r\n              id=\"Path_113807\"\r\n              data-name=\"Path 113807\"\r\n              d=\"M13.748,7.475a6.273,6.273,0,1,0,6.273,6.273A6.276,6.276,0,0,0,13.748,7.475Zm.369.75a5.535,5.535,0,0,1,0,11.046ZM12.7,4.095v1.7a1.048,1.048,0,0,0,2.1,0v-1.7a1.048,1.048,0,0,0-2.1,0Zm7.133,2.087-1.2,1.2a1.048,1.048,0,0,0,1.482,1.482l1.2-1.2a1.048,1.048,0,1,0-1.482-1.482ZM23.4,12.7h-1.7a1.048,1.048,0,0,0,0,2.1h1.7a1.048,1.048,0,0,0,0-2.1Zm-2.087,7.133-1.2-1.2a1.048,1.048,0,0,0-1.482,1.482l1.2,1.2a1.048,1.048,0,1,0,1.482-1.482ZM14.8,23.4v-1.7a1.048,1.048,0,0,0-2.1,0v1.7a1.048,1.048,0,0,0,2.1,0ZM7.663,21.315l1.2-1.2a1.048,1.048,0,0,0-1.482-1.482l-1.2,1.2a1.048,1.048,0,1,0,1.482,1.482ZM4.095,14.8h1.7a1.048,1.048,0,0,0,0-2.1h-1.7a1.048,1.048,0,0,0,0,2.1ZM6.181,7.663l1.2,1.2A1.048,1.048,0,0,0,8.863,7.381l-1.2-1.2A1.048,1.048,0,0,0,6.181,7.663Z\"\r\n              fill=\"#fff\"\r\n              fillRule=\"evenodd\"\r\n            />\r\n          </g>\r\n        </g>\r\n      </g>\r\n    </svg>\r\n  );\r\n};\r\nexport const GlobalIcons = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-global.svg\" alt=\"Global Icons\" />\r\n  );\r\n};\r\nexport const GlobalBlueIcons = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-blue-global.svg\" alt=\"Global Blue Icons\" />\r\n  );\r\n};\r\nexport const UserBlackIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-black.svg\" alt=\"User Black Icon\" />\r\n  );\r\n};\r\nexport const UserBluekIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-brand-blue.svg\" alt=\"User Blue Icon\" />\r\n  );\r\n};\r\nexport const UserSolidBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-account.svg\" alt=\"User Solid Blue Icon\" />\r\n  );\r\n};\r\nexport const SearchIcons = ({ width = 18, height = 18 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-search.svg\" width={width} height={height} alt=\"Search Icon\" />\r\n  );\r\n};\r\nexport const RoketIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-rocket.svg\" alt=\"Rocket Icon\" />\r\n  );\r\n};\r\nexport const FilterIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-filter.svg\" alt=\"Filter Icon\" />\r\n  );\r\n};\r\nexport const DashboardIcon = ({ color }) => {\r\n  return (\r\n    <img\r\n      src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-dashboard.svg\"\r\n      alt=\"Dashboard Icon\"\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const DynamicIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-dynamic.svg\" alt=\"Dynamic Icon\" />\r\n  );\r\n};\r\nexport const KpiIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kpi.svg\" alt=\"KPI Icon\" />\r\n  );\r\n};\r\nexport const GraphsIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-graphs.svg\" alt=\"Graph Icon\" />\r\n  );\r\n};\r\nexport const ChartIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-charts.svg\" alt=\"Chart Icon\" />\r\n  );\r\n};\r\nexport const TrendIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trend.svg\" alt=\"Trend Icon\" />\r\n  );\r\n};\r\nexport const RealTimeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-real-time.svg\" alt=\"Real Time Icon\" />\r\n  );\r\n};\r\nexport const BrushIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-customize.svg\" alt=\"Brush Icon\" />\r\n  );\r\n};\r\nexport const LearningIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-learning.svg\" alt=\"Learning Icon\" />\r\n  );\r\n};\r\nexport const NextArrowIcon = () => {\r\n  return (\r\n    // <svg\r\n    //   width=\"26\"\r\n    //   height=\"22\"\r\n    //   viewBox=\"0 0 26 22\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M13.9387 21.1081C13.7989 20.9687 13.6879 20.8031 13.6122 20.6208C13.5365 20.4385 13.4975 20.243 13.4975 20.0456C13.4975 19.8481 13.5365 19.6527 13.6122 19.4703C13.6879 19.288 13.7989 19.1224 13.9387 18.9831L20.3749 12.5468L1.99995 12.5468C1.60212 12.5468 1.2206 12.3888 0.93929 12.1075C0.657985 11.8262 0.49995 11.4446 0.49995 11.0468C0.49995 10.649 0.657985 10.2675 0.939291 9.98616C1.2206 9.70485 1.60212 9.54682 1.99995 9.54682L20.3749 9.54682L13.9387 3.10807C13.6569 2.82628 13.4986 2.44409 13.4986 2.04557C13.4986 1.64706 13.6569 1.26486 13.9387 0.98307C14.2205 0.701278 14.6027 0.542968 15.0012 0.542968C15.3997 0.542968 15.7819 0.701278 16.0637 0.98307L25.0637 9.98307C25.2035 10.1224 25.3145 10.288 25.3902 10.4703C25.4659 10.6527 25.5049 10.8481 25.5049 11.0456C25.5049 11.243 25.4659 11.4385 25.3902 11.6208C25.3145 11.8031 25.2035 11.9687 25.0637 12.1081L16.0637 21.1081C15.9243 21.2479 15.7588 21.3589 15.5764 21.4346C15.3941 21.5103 15.1986 21.5493 15.0012 21.5493C14.8038 21.5493 14.6083 21.5103 14.426 21.4346C14.2436 21.3589 14.0781 21.2479 13.9387 21.1081Z\"\r\n    //     fill=\"white\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-next-arrow.svg\" alt=\"Next Arrow Icon\" />\r\n  );\r\n};\r\nexport const PrevIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-prev-arrow.svg\" alt=\"Prev Icon\" />\r\n  );\r\n};\r\nexport const QuoteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-quote.svg\" alt=\"Quote Icon\"\r\n      width=\"31\"\r\n      height=\"26\"\r\n    />\r\n  );\r\n};\r\nexport const CheckIcon = ({ height = 28, width = 28 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success.svg\" alt=\"Check Icon\"\r\n      width={width}\r\n      height={height}\r\n    />\r\n  );\r\n};\r\nexport const RedCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-x.svg\" alt=\"Red Cross Icon\" />\r\n  );\r\n};\r\nexport const PlusIcon = ({ color, height = 32, width = 33 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\" alt=\"Plus Icon\"\r\n      width={width}\r\n      height={height}\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const MinusIcon = ({ width = '29px', height = '4px' }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg\" height={height} width={width} alt=\"Minus Icon\" />\r\n  );\r\n};\r\nexport const RedInfoStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-warning-star.svg\" alt=\"Red Info Star Icon\" />\r\n  );\r\n};\r\nexport const GreenCheckStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-green-check-star.svg\" alt=\"Green Check Star\"\r\n      width=\"42\"\r\n      height=\"42\"\r\n    />\r\n  );\r\n};\r\nexport const NoteSettingBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-note-setting.svg\" alt=\"Note Setting Blue Icon\" />\r\n  );\r\n};\r\nexport const SettingIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"38\"\r\n      height=\"40\"\r\n      viewBox=\"0 0 38 40\"\r\n      fill=\"none\">\r\n      <path\r\n        d=\"M19.0264 0.5C20.4944 0.516 21.9564 0.686 23.3904 1.006C23.6955 1.0741 23.9716 1.23581 24.1803 1.46852C24.389 1.70124 24.5198 1.99334 24.5544 2.304L24.8944 5.358C24.9424 5.78857 25.0908 6.20186 25.3277 6.56461C25.5645 6.92737 25.8832 7.22946 26.2581 7.44658C26.633 7.6637 27.0537 7.78979 27.4862 7.8147C27.9187 7.83961 28.351 7.76264 28.7484 7.59L31.5484 6.36C31.8329 6.2347 32.1493 6.20088 32.4538 6.26323C32.7584 6.32557 33.036 6.48099 33.2484 6.708C35.2725 8.87031 36.7803 11.4633 37.6584 14.292C37.7501 14.5895 37.7471 14.9081 37.6496 15.2038C37.5521 15.4994 37.3651 15.7574 37.1144 15.942L34.6324 17.774C34.2827 18.0303 33.9983 18.3655 33.8023 18.7522C33.6063 19.1389 33.5041 19.5664 33.5041 20C33.5041 20.4336 33.6063 20.8611 33.8023 21.2478C33.9983 21.6345 34.2827 21.9697 34.6324 22.226L37.1184 24.056C37.3695 24.2407 37.5568 24.499 37.6543 24.7951C37.7518 25.0912 37.7546 25.4102 37.6624 25.708C36.7849 28.5366 35.2778 31.1295 33.2544 33.292C33.0424 33.5189 32.7652 33.6745 32.4611 33.7372C32.1569 33.7999 31.8408 33.7666 31.5564 33.642L28.7444 32.408C28.3476 32.234 27.9154 32.1559 27.4827 32.18C27.0501 32.204 26.6292 32.3296 26.2542 32.5466C25.8791 32.7635 25.5604 33.0657 25.3238 33.4287C25.0872 33.7917 24.9394 34.2053 24.8924 34.636L24.5524 37.688C24.5184 37.995 24.3905 38.2841 24.1861 38.5157C23.9817 38.7473 23.7108 38.9101 23.4104 38.982C20.5136 39.6726 17.4951 39.6726 14.5984 38.982C14.2976 38.9105 14.0262 38.7478 13.8214 38.5162C13.6167 38.2845 13.4885 37.9953 13.4544 37.688L13.1164 34.64C13.0673 34.2106 12.9182 33.7987 12.6811 33.4374C12.4439 33.0761 12.1254 32.7754 11.751 32.5595C11.3766 32.3437 10.9568 32.2186 10.5253 32.1943C10.0938 32.1701 9.6626 32.2474 9.26638 32.42L6.45438 33.652C6.1697 33.7771 5.85318 33.8106 5.54862 33.7479C5.24406 33.6852 4.96652 33.5293 4.75438 33.302C2.73064 31.137 1.22419 28.5412 0.348384 25.71C0.256177 25.4122 0.259017 25.0932 0.35651 24.7971C0.454002 24.501 0.641304 24.2427 0.892384 24.058L3.37838 22.226C3.72808 21.9697 4.01246 21.6345 4.20848 21.2478C4.40451 20.8611 4.50666 20.4336 4.50666 20C4.50666 19.5664 4.40451 19.1389 4.20848 18.7522C4.01246 18.3655 3.72808 18.0303 3.37838 17.774L0.892384 15.946C0.641304 15.7613 0.454002 15.503 0.35651 15.2069C0.259017 14.9108 0.256177 14.5918 0.348384 14.294C1.22647 11.4653 2.73423 8.87231 4.75838 6.71C4.97075 6.48299 5.24841 6.32757 5.55296 6.26523C5.8575 6.20288 6.17389 6.2367 6.45838 6.362L9.25838 7.592C9.65642 7.76454 10.0894 7.84132 10.5225 7.81617C10.9556 7.79102 11.3767 7.66465 11.7521 7.44719C12.1275 7.22974 12.4467 6.92727 12.684 6.56408C12.9212 6.2009 13.07 5.78712 13.1184 5.356L13.4584 2.304C13.4927 1.99271 13.6236 1.69997 13.8327 1.46683C14.0418 1.23369 14.3186 1.07185 14.6244 1.004C16.0564 0.686667 17.5237 0.518667 19.0264 0.5ZM19.0024 14C17.4111 14 15.885 14.6321 14.7597 15.7574C13.6345 16.8826 13.0024 18.4087 13.0024 20C13.0024 21.5913 13.6345 23.1174 14.7597 24.2426C15.885 25.3679 17.4111 26 19.0024 26C20.5937 26 22.1198 25.3679 23.245 24.2426C24.3702 23.1174 25.0024 21.5913 25.0024 20C25.0024 18.4087 24.3702 16.8826 23.245 15.7574C22.1198 14.6321 20.5937 14 19.0024 14Z\"\r\n        fill=\"#FEA500\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const SolidSettingIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-yellow-wrench.svg\" alt=\"Solid Setting Icon\" />\r\n  );\r\n};\r\nexport const RightSolidArrowIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-solid-arrow.svg\" alt=\"\" />\r\n  );\r\n};\r\nexport const SolidInfoIcon = ({ width = '20px', height = '20px' }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg\" height={height} width={width} alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\nexport const WhiteInfoIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-white.svg\" alt=\"White Info Marker Icon\" />\r\n  );\r\n};\r\nexport const RightArrowIcon = ({ color }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg\" alt=\"Right Arrow Icon\"\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const CartIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"25\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 25 20\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M20.8333 16C19.3533 16 18.1667 16.89 18.1667 18C18.1667 18.5304 18.4476 19.0391 18.9477 19.4142C19.4478 19.7893 20.1261 20 20.8333 20C21.5406 20 22.2189 19.7893 22.719 19.4142C23.219 19.0391 23.5 18.5304 23.5 18C23.5 17.4696 23.219 16.9609 22.719 16.5858C22.2189 16.2107 21.5406 16 20.8333 16ZM-0.5 0V2H2.16667L6.96667 9.59L5.15333 12.04C4.95333 12.32 4.83333 12.65 4.83333 13C4.83333 13.5304 5.11428 14.0391 5.61438 14.4142C6.11448 14.7893 6.79276 15 7.5 15H23.5V13H8.06C7.97159 13 7.88681 12.9737 7.8243 12.9268C7.76178 12.8799 7.72667 12.8163 7.72667 12.75C7.72667 12.7 7.74 12.66 7.76667 12.63L8.96667 11H18.9C19.9 11 20.78 10.58 21.2333 9.97L26.0067 3.5C26.1 3.34 26.1667 3.17 26.1667 3C26.1667 2.73478 26.0262 2.48043 25.7761 2.29289C25.5261 2.10536 25.187 2 24.8333 2H5.11333L3.86 0M7.5 16C6.02 16 4.83333 16.89 4.83333 18C4.83333 18.5304 5.11428 19.0391 5.61438 19.4142C6.11448 19.7893 6.79276 20 7.5 20C8.20724 20 8.88552 19.7893 9.38562 19.4142C9.88571 19.0391 10.1667 18.5304 10.1667 18C10.1667 17.4696 9.88571 16.9609 9.38562 16.5858C8.88552 16.2107 8.20724 16 7.5 16Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const SignoutIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-signout.svg\" alt=\"Signout Icon\" />\r\n  );\r\n};\r\nexport const HelpIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-helpicon.svg\" alt=\"Help Icon\" />\r\n  );\r\n};\r\nexport const BaseEyeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye.svg\" alt=\"Base Eye Icon\" />\r\n  );\r\n};\r\nexport const UserBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-blue.svg\" alt=\"User Blue Icon\" />\r\n  );\r\n};\r\nexport const DollerIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-renew-dollar.svg\" alt=\"Dollar Icon\" />\r\n  );\r\n};\r\nexport const SecurityIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-security.svg\" alt=\"Security Icon\" />\r\n  );\r\n};\r\nexport const LockIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-lock.svg\" alt=\"Lock Icon\" />\r\n  );\r\n};\r\nexport const LinkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-links.svg\" alt=\"Link Icon\" />\r\n  );\r\n};\r\nexport const PaymentIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-payment.svg\" alt=\"Payment Icon\" />\r\n  );\r\n};\r\nexport const PaymentIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"20\"\r\n      height=\"17\"\r\n      viewBox=\"0 0 20 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M18 0.5H2C0.89 0.5 0.00999999 1.39 0.00999999 2.5L0 14.5C0 15.61 0.89 16.5 2 16.5H18C19.11 16.5 20 15.61 20 14.5V2.5C20 1.39 19.11 0.5 18 0.5ZM18 14.5H2V8.5H18V14.5ZM18 4.5H2V2.5H18V4.5Z\"\r\n        fill=\"#00ADEF\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const CartSideIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-carticon.svg\" alt=\"Cart Icon\" />\r\n  );\r\n};\r\nexport const EditIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-editcon.svg\" alt=\"Edit Icon\" />\r\n  );\r\n};\r\n\r\nexport const CrossIcon = ({ color }) => {\r\n  return (\r\n    // <svg\r\n    //   width=\"18\"\r\n    //   height=\"19\"\r\n    //   viewBox=\"0 0 18 19\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z\"\r\n    //     fill=\"white\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-x.svg\" alt=\"White Cirle Cross Icon\"\r\n      width=\"18\"\r\n      height=\"19\"\r\n\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const PublicProfileIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-light.svg\" alt=\"Public Profile Icon\" />\r\n  );\r\n};\r\nexport const SellerDashboardIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge.svg\" alt=\"Seller Dashboard Icon\" />\r\n  );\r\n};\r\nexport const MarketplaceDisputeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gavel.svg\" alt=\"Marketplace Dispute Icon\" />\r\n  );\r\n};\r\nexport const MarketplaceListIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-marketplace-icon.svg\" alt=\"Marketplace List Icon\" />\r\n  );\r\n};\r\nexport const PurchasedProductIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-package.svg\" alt=\"Purchased Product Icon\" />\r\n  );\r\n};\r\nexport const SoldProductIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coin.svg\" alt=\"Sold Product Icon\" />\r\n  );\r\n};\r\nexport const LogoutIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M2 18C1.45 18 0.979333 17.8043 0.588 17.413C0.196667 17.0217 0.000666667 16.5507 0 16V2C0 1.45 0.196 0.979333 0.588 0.588C0.98 0.196667 1.45067 0.000666667 2 0H9V2H2V16H9V18H2ZM13 14L11.625 12.55L14.175 10H6V8H14.175L11.625 5.45L13 4L18 9L13 14Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const CheckGradientIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"211\"\r\n      height=\"208\"\r\n      viewBox=\"0 0 211 208\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M203.341 74.5632C206.051 83.8216 207.504 93.6158 207.504 103.752C207.504 161.052 161.052 207.504 103.752 207.504C46.4519 207.504 0 161.052 0 103.752C0 46.4518 46.4519 -9.15527e-05 103.752 -9.15527e-05C131.892 -9.15527e-05 157.416 11.2021 176.105 29.3936L194.876 22.6914L101.952 115.617L63.9188 77.5842L39.9479 101.556L101.952 163.559L210.127 55.3835L203.341 74.5632Z\"\r\n        fill=\"url(#paint0_radial_490_1337)\"\r\n      />\r\n      <defs>\r\n        <radialGradient\r\n          id=\"paint0_radial_490_1337\"\r\n          cx=\"0\"\r\n          cy=\"0\"\r\n          r=\"1\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n          gradientTransform=\"translate(59.8666 54.0097) rotate(45) scale(197.976)\"\r\n        >\r\n          <stop stopColor=\"#73D1E1\" />\r\n          <stop offset=\"1\" stopColor=\"#395BB2\" />\r\n        </radialGradient>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\nexport const CalculatorIcon = () => {\r\n  return (\r\n    // <svg\r\n    //   width=\"38\"\r\n    //   height=\"38\"\r\n    //   viewBox=\"0 0 38 38\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M10.6667 31.5H13.7917V27.3333H17.9583V24.2083H13.7917V20.0417H10.6667V24.2083H6.5V27.3333H10.6667V31.5ZM21.0833 29.9375H31.5V26.8125H21.0833V29.9375ZM21.0833 24.7292H31.5V21.6042H21.0833V24.7292ZM23.375 16.8125L26.2917 13.8958L29.2083 16.8125L31.3958 14.625L28.4792 11.6042L31.3958 8.6875L29.2083 6.5L26.2917 9.41667L23.375 6.5L21.1875 8.6875L24.1042 11.6042L21.1875 14.625L23.375 16.8125ZM7.02084 13.1667H17.4375V10.0417H7.02084V13.1667ZM4.41667 37.75C3.27084 37.75 2.28959 37.3424 1.47292 36.5271C0.656253 35.7118 0.248615 34.7306 0.250004 33.5833V4.41667C0.250004 3.27083 0.658337 2.29028 1.475 1.475C2.29167 0.659722 3.27223 0.251389 4.41667 0.25H33.5833C34.7292 0.25 35.7104 0.658333 36.5271 1.475C37.3438 2.29167 37.7514 3.27222 37.75 4.41667V33.5833C37.75 34.7292 37.3424 35.7104 36.5271 36.5271C35.7118 37.3438 34.7306 37.7514 33.5833 37.75H4.41667Z\"\r\n    //     fill=\"#00ADEF\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-calculator.svg\" alt=\"\" />\r\n  );\r\n};\r\nexport const SolidRedArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"31\"\r\n      height=\"36\"\r\n      viewBox=\"0 0 31 36\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M28.5 13.6699C31.8333 15.5944 31.8333 20.4056 28.5 22.3301L7.5 34.4545C4.16666 36.379 -1.74729e-06 33.9734 -1.57905e-06 30.1243L-5.19101e-07 5.87564C-3.50856e-07 2.02664 4.16667 -0.378984 7.5 1.54552L28.5 13.6699Z\"\r\n        fill=\"#FF696A\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropArrowIcon = ({ width = 24, height = 13 }) => {\r\n  return (\r\n    <svg\r\n      width={width}\r\n      height={height}\r\n      viewBox=\"0 0 24 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropArrowUpIcon = ({ width = 24, height = 13 }) => {\r\n  return (\r\n    <svg\r\n      width={width}\r\n      height={height}\r\n      viewBox=\"0 0 24 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      style={{ transform: 'rotate(180deg)' }}\r\n    >\r\n      <path\r\n        d=\"M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropDownArrowIcon = ({ width = 14, height = 7 }) => {\r\n  return (\r\n    <svg width={width}\r\n      height={height}\r\n      viewBox=\"0 0 14 7\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M0.333008 0.333984L6.99967 7.00065L13.6663 0.333984H0.333008Z\" fill=\"white\" />\r\n    </svg>\r\n\r\n  );\r\n};\r\nexport const BlackDropDownArrowIcon = ({ width = 14, height = 7 }) => {\r\n  return (\r\n    <svg width={width}\r\n      height={height}\r\n      viewBox=\"0 0 14 7\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M0.333252 0.333008L6.99992 6.99967L13.6666 0.333008H0.333252Z\" fill=\"#666666\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const DeleteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-delete.svg\" alt=\"Delete Icon\"\r\n      width=\"20\"\r\n      height=\"21\"\r\n    />\r\n  );\r\n};\r\nexport const TradeIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"48\"\r\n      height=\"48\"\r\n      viewBox=\"0 0 48 48\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M0.75 42.0833H47.25V47.25H0.75V42.0833ZM8.5 34.3333C11.3417 34.3333 13.6667 32.0083 13.6667 29.1667C13.6667 27.875 13.15 26.5833 12.375 25.8083L15.7333 18.8333H16.25C17.5417 18.8333 18.8333 18.3167 19.6083 17.5417L26.5833 21.1583V21.4167C26.5833 24.2583 28.9083 26.5833 31.75 26.5833C34.5917 26.5833 36.9167 24.2583 36.9167 21.4167C36.9167 20.125 36.4 19.0917 35.625 18.0583L38.9833 11.0833H39.5C42.3417 11.0833 44.6667 8.75833 44.6667 5.91667C44.6667 3.075 42.3417 0.75 39.5 0.75C36.6583 0.75 34.3333 3.075 34.3333 5.91667C34.3333 7.20833 34.85 8.5 35.625 9.275L32.2667 16.25H31.75C30.4583 16.25 29.1667 16.7667 28.3917 17.5417L21.4167 14.1833V13.6667C21.4167 10.825 19.0917 8.5 16.25 8.5C13.4083 8.5 11.0833 10.825 11.0833 13.6667C11.0833 14.9583 11.6 16.25 12.375 17.025L9.01667 24H8.5C5.65833 24 3.33333 26.325 3.33333 29.1667C3.33333 32.0083 5.65833 34.3333 8.5 34.3333Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ArtArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"30\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 30 32\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M1.64673 22.9062L27.6275 7.90625\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      <path\r\n        d=\"M1.64673 15.0469L8.64673 8.04688\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      <path\r\n        d=\"M3.14673 29.0469H13.0462\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const RedInfoIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg\" alt=\"Red Info Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const GreyCheckIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success-gray.svg\" alt=\"Grey Success Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const GreyCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure.svg\" alt=\"Grey Cross Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const ReferIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-refer-a-friend.svg\" alt=\"Refer Icon\" />\r\n  );\r\n};\r\nexport const PartnershipIcon = () => {\r\n  return (\r\n    <svg\r\n      id=\"Layer_1\"\r\n      data-name=\"Layer 1\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"20\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 24 24\"\r\n    >\r\n      <path\r\n        d=\"m17.063,2.185c-1.245.06-2.442.603-3.367,1.528l-3.63,3.63c.57-.573,2.687-.179,3.2.334l2.197-2.197c.487-.487,1.096-.785,1.719-.812.424-.021,1.024.069,1.552.597.493.493.597,1.066.597,1.457,0,.654-.299,1.304-.812,1.815l-3.821,3.845c-.961.961-2.424,1.039-3.272.191-.484-.484-1.281-.487-1.767,0s-.487,1.281,0,1.767c.872.872,2.018,1.313,3.2,1.313,1.278,0,2.582-.522,3.582-1.528l3.845-3.821c.976-.973,1.528-2.275,1.528-3.582,0-1.215-.46-2.37-1.313-3.224-.913-.913-2.14-1.373-3.439-1.313Zm-5.922,6.161c-1.278,0-2.603.525-3.606,1.528l-3.821,3.821c-.976.973-1.528,2.275-1.528,3.582,0,1.215.46,2.37,1.313,3.224.913.913,2.14,1.373,3.439,1.313,1.245-.06,2.442-.603,3.367-1.528l3.63-3.63c-.573.573-2.687.179-3.2-.334l-2.197,2.197c-.487.487-1.096.782-1.719.812-.424.021-1.024-.069-1.552-.597-.493-.493-.597-1.069-.597-1.457,0-.654.299-1.304.812-1.815l3.821-3.845c.961-.961,2.424-1.036,3.272-.191.487.487,1.284.487,1.767,0,.487-.487.487-1.281,0-1.767-.872-.872-2.021-1.313-3.2-1.313Z\"\r\n        fill=\"#fff\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ResendCodeIcon = ({ isRotating }) => {\r\n  return (\r\n    <svg\r\n      className={isRotating ? \"rotate\" : \"\"}\r\n      width=\"19\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 19 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M18.7693 1.84688V6.34688C18.7693 6.54579 18.6903 6.73656 18.5497 6.87721C18.409 7.01786 18.2182 7.09688 18.0193 7.09688H13.5193C13.3717 7.09664 13.2275 7.05285 13.1047 6.97101C12.9818 6.88917 12.8859 6.77291 12.8289 6.63679C12.7718 6.50066 12.7562 6.35074 12.784 6.20578C12.8117 6.06082 12.8816 5.92728 12.985 5.82188L14.71 4.09688L14.3068 3.69375C13.2576 2.64569 11.9212 1.93222 10.4666 1.6435C9.01196 1.35479 7.50438 1.5038 6.13442 2.07171C4.76446 2.63961 3.59362 3.60091 2.76988 4.83409C1.94613 6.06728 1.50648 7.517 1.50648 9C1.50648 10.483 1.94613 11.9327 2.76988 13.1659C3.59362 14.3991 4.76446 15.3604 6.13442 15.9283C7.50438 16.4962 9.01196 16.6452 10.4666 16.3565C11.9212 16.0678 13.2576 15.3543 14.3068 14.3063C14.3758 14.2357 14.4582 14.1796 14.5492 14.1413C14.6401 14.103 14.7378 14.0833 14.8365 14.0833C14.9352 14.0833 15.0329 14.103 15.1239 14.1413C15.2148 14.1796 15.2972 14.2357 15.3662 14.3063C15.5065 14.4469 15.5852 14.6373 15.5852 14.8359C15.5852 15.0345 15.5065 15.225 15.3662 15.3656C14.1073 16.6238 12.5037 17.4805 10.758 17.8274C9.01229 18.1743 7.20293 17.9958 5.55867 17.3145C3.91441 16.6331 2.50908 15.4796 1.52035 13.9996C0.531628 12.5197 0.00390625 10.7798 0.00390625 9C0.00390625 7.22017 0.531628 5.4803 1.52035 4.00036C2.50908 2.52042 3.91441 1.36687 5.55867 0.685539C7.20293 0.00421169 9.01229 -0.174293 10.758 0.172592C12.5037 0.519478 14.1073 1.37618 15.3662 2.63438L15.7693 3.0375L17.485 1.32188C17.589 1.21629 17.722 1.14391 17.8672 1.11388C18.0124 1.08384 18.1632 1.0975 18.3006 1.15313C18.4374 1.21109 18.5545 1.30747 18.6377 1.43059C18.7209 1.55371 18.7666 1.69831 18.7693 1.84688Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ContactCustomerSupport = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 0.875C7.39303 0.875 5.82214 1.35152 4.486 2.24431C3.14985 3.1371 2.10844 4.40605 1.49348 5.8907C0.87852 7.37535 0.717618 9.00901 1.03112 10.5851C1.34463 12.1612 2.11846 13.6089 3.25476 14.7452C4.39106 15.8815 5.8388 16.6554 7.4149 16.9689C8.99099 17.2824 10.6247 17.1215 12.1093 16.5065C13.594 15.8916 14.8629 14.8502 15.7557 13.514C16.6485 12.1779 17.125 10.607 17.125 9C17.1209 6.84638 16.2635 4.78216 14.7407 3.25932C13.2178 1.73648 11.1536 0.87913 9 0.875ZM9 14C8.81458 14 8.63333 13.945 8.47916 13.842C8.32499 13.739 8.20482 13.5926 8.13387 13.4213C8.06291 13.25 8.04434 13.0615 8.08052 12.8796C8.11669 12.6977 8.20598 12.5307 8.33709 12.3996C8.4682 12.2685 8.63525 12.1792 8.81711 12.143C8.99896 12.1068 9.18746 12.1254 9.35877 12.1964C9.53007 12.2673 9.67649 12.3875 9.77951 12.5417C9.88252 12.6958 9.9375 12.8771 9.9375 13.0625C9.9375 13.3111 9.83873 13.5496 9.66292 13.7254C9.4871 13.9012 9.24864 14 9 14ZM9.625 10.1797V10.25C9.625 10.4158 9.55916 10.5747 9.44195 10.6919C9.32474 10.8092 9.16576 10.875 9 10.875C8.83424 10.875 8.67527 10.8092 8.55806 10.6919C8.44085 10.5747 8.375 10.4158 8.375 10.25V9.625C8.375 9.45924 8.44085 9.30027 8.55806 9.18306C8.67527 9.06585 8.83424 9 9 9C9.30904 9 9.61113 8.90836 9.86808 8.73667C10.125 8.56498 10.3253 8.32095 10.4436 8.03544C10.5618 7.74993 10.5928 7.43577 10.5325 7.13267C10.4722 6.82958 10.3234 6.55117 10.1049 6.33265C9.88634 6.11413 9.60793 5.96531 9.30483 5.90502C9.00174 5.84473 8.68757 5.87568 8.40206 5.99394C8.11655 6.1122 7.87252 6.31247 7.70083 6.56942C7.52914 6.82637 7.4375 7.12847 7.4375 7.4375C7.4375 7.60326 7.37166 7.76223 7.25445 7.87944C7.13724 7.99665 6.97826 8.0625 6.8125 8.0625C6.64674 8.0625 6.48777 7.99665 6.37056 7.87944C6.25335 7.76223 6.1875 7.60326 6.1875 7.4375C6.18751 6.90805 6.33695 6.38936 6.61865 5.94107C6.90036 5.49279 7.30287 5.13312 7.77991 4.90344C8.25694 4.67376 8.78912 4.58339 9.31523 4.64273C9.84134 4.70207 10.34 4.90871 10.7539 5.23888C11.1678 5.56905 11.4801 6.00934 11.6549 6.50911C11.8296 7.00888 11.8598 7.54784 11.7418 8.06398C11.6239 8.58013 11.3627 9.05251 10.9882 9.42678C10.6137 9.80106 10.1412 10.062 9.625 10.1797Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n\r\n\r\n  );\r\n};\r\nexport const RedErrorCircle = () => {\r\n  return (\r\n    // <svg\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    //   width=\"24\"\r\n    //   height=\"24\"\r\n    //   viewBox=\"0 0 24 24\"\r\n    //   fill=\"#ff696a\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M11.953 2C6.465 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.493 2 11.953 2zM13 17h-2v-2h2v2zm0-4h-2V7h2v6z\"\r\n    //     fill=\"#fffff\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg\"\r\n      alt=\"Red Error Icon\"\r\n      width=\"24\"\r\n      height=\"24\"\r\n    />\r\n  );\r\n};\r\nexport const BlackErrorCircle = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"17\"\r\n      viewBox=\"0 0 18 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M9 0.375C9 0.375 10.6526 0.375 12.1628 1.01376C12.1628 1.01376 13.621 1.63053 14.7452 2.75476C14.7452 2.75476 15.8695 3.87899 16.4862 5.33719C16.4862 5.33719 17.125 6.84739 17.125 8.5C17.125 8.5 17.125 10.1526 16.4862 11.6628C16.4862 11.6628 15.8695 13.121 14.7452 14.2452C14.7452 14.2452 13.621 15.3695 12.1628 15.9862C12.1628 15.9862 10.6526 16.625 9 16.625C9 16.625 7.34739 16.625 5.83719 15.9862C5.83719 15.9862 4.37899 15.3695 3.25476 14.2452C3.25476 14.2452 2.13053 13.121 1.51376 11.6628C1.51376 11.6628 0.875 10.1526 0.875 8.5C0.875 8.5 0.875 6.84739 1.51376 5.33719C1.51376 5.33719 2.13053 3.87899 3.25476 2.75476C3.25476 2.75476 4.37899 1.63053 5.83719 1.01376C5.83719 1.01376 7.34739 0.375 9 0.375ZM9 1.625C9 1.625 7.60087 1.625 6.32413 2.16501C6.32413 2.16501 5.09047 2.68681 4.13864 3.63864C4.13864 3.63864 3.18681 4.59047 2.66502 5.82413C2.66502 5.82413 2.125 7.10087 2.125 8.5C2.125 8.5 2.125 9.89913 2.66502 11.1759C2.66502 11.1759 3.18681 12.4095 4.13864 13.3614C4.13864 13.3614 5.09047 14.3132 6.32413 14.835C6.32413 14.835 7.60087 15.375 9 15.375C9 15.375 10.3991 15.375 11.6759 14.835C11.6759 14.835 12.9095 14.3132 13.8614 13.3614C13.8614 13.3614 14.8132 12.4095 15.335 11.1759C15.335 11.1759 15.875 9.89912 15.875 8.5C15.875 8.5 15.875 7.10087 15.335 5.82413C15.335 5.82413 14.8132 4.59047 13.8614 3.63864C13.8614 3.63864 12.9095 2.68681 11.6759 2.16501C11.6759 2.16501 10.3991 1.625 9 1.625Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n      <path\r\n        d=\"M9 12.875H9.625C9.97018 12.875 10.25 12.5952 10.25 12.25C10.25 11.9048 9.97018 11.625 9.625 11.625V7.875C9.625 7.52982 9.34518 7.25 9 7.25H8.375C8.02982 7.25 7.75 7.52982 7.75 7.875C7.75 8.22018 8.02982 8.5 8.375 8.5V12.25C8.375 12.5952 8.65482 12.875 9 12.875Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n      <path\r\n        d=\"M9.78125 5.0625C9.78125 5.58027 9.36148 6 8.84375 6C8.32602 6 7.90625 5.58027 7.90625 5.0625C7.90625 4.54473 8.32602 4.125 8.84375 4.125C9.36148 4.125 9.78125 4.54473 9.78125 5.0625Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n    </svg>\r\n\r\n  );\r\n};\r\nexport const DeviceMobileSpeaker = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-device-00ADEF.svg\" alt=\"Device Mobile Speaker Icon\"\r\n      width=\"22\"\r\n      height=\"22\"\r\n    />\r\n\r\n  );\r\n};\r\nexport const ViewCartBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-00ADEF.svg\" alt=\"View Cart BaseBlue\" />\r\n  );\r\n};\r\nexport const ViewCartDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-04498C.svg\" alt=\"View Cart Dark Blue\" />\r\n\r\n  );\r\n};\r\nexport const ViewCartGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-808080.svg\" alt=\"View Cart Gray\" />\r\n\r\n  );\r\n};\r\nexport const CheckoutCardBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-00ADEF.svg\" alt=\"Checkout Card BaseBlue\" />\r\n  );\r\n};\r\nexport const CheckoutCardDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-04498C.svg\" alt=\"Checkout Card BaseBlue\" />\r\n  );\r\n};\r\nexport const CheckoutCardGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-808080.svg\" alt=\"Checkout Card Gray\" />\r\n  );\r\n};\r\nexport const AccessBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-00ADEF.svg\" alt=\"Access Base Blue\" />\r\n  );\r\n};\r\nexport const AccessDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-04498C.svg\" alt=\"Access Dark Blue\" />\r\n  );\r\n};\r\nexport const AccessGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-808080.svg\" alt=\"Access Gray\" />\r\n  );\r\n};\r\nexport const TopRightArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"13\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 13 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M0.832189 10.79L9.12219 2.5H3.54219C3.27697 2.5 3.02262 2.39464 2.83508 2.20711C2.64754 2.01957 2.54219 1.76522 2.54219 1.5C2.54219 1.23478 2.64754 0.98043 2.83508 0.792893C3.02262 0.605357 3.27697 0.5 3.54219 0.5H11.4922C11.7574 0.5 12.0118 0.605357 12.1993 0.792893C12.3868 0.98043 12.4922 1.23478 12.4922 1.5V9.5C12.4922 9.76522 12.3868 10.0196 12.1993 10.2071C12.0118 10.3946 11.7574 10.5 11.4922 10.5H11.5422C11.277 10.5 11.0226 10.3946 10.8351 10.2071C10.6475 10.0196 10.5422 9.76522 10.5422 9.5V3.95L2.28219 12.21C2.18922 12.3037 2.07862 12.3781 1.95677 12.4289C1.83491 12.4797 1.7042 12.5058 1.57219 12.5058C1.44018 12.5058 1.30947 12.4797 1.18761 12.4289C1.06575 12.3781 0.955151 12.3037 0.862187 12.21C0.766468 12.119 0.689713 12.01 0.636353 11.8892C0.582994 11.7684 0.554085 11.6383 0.551295 11.5063C0.548506 11.3742 0.571892 11.243 0.620103 11.12C0.668314 10.9971 0.740396 10.8849 0.832189 10.79Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const MoneyWithWings = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-money-wings.svg\" alt=\"Money Wings Icon\" />\r\n  );\r\n};\r\nexport const FlatBlueBook = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-book-00A6ED.svg\" alt=\"Flat Blue Book\" />\r\n  );\r\n};\r\n\r\nexport const RedCircleCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure-red.svg\" alt=\"Red Cross\" />\r\n  );\r\n};\r\n\r\nexport const WhiteCrossCircle = () => {\r\n  return (\r\n    <svg width=\"28\" height=\"28\" viewBox=\"0 0 28 28\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M14 0C6.2 0 0 6.2 0 14C0 21.8 6.2 28 14 28C21.8 28 28 21.8 28 14C28 6.2 21.8 0 14 0ZM19.4 21L14 15.6L8.6 21L7 19.4L12.4 14L7 8.6L8.6 7L14 12.4L19.4 7L21 8.6L15.6 14L21 19.4L19.4 21Z\" fill=\"white\" />\r\n    </svg>\r\n\r\n  )\r\n}\r\nexport const BlackCross = ({ width = 14, height = 14 }) => {\r\n  return (\r\n    <svg width={width} height={height} viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M1.4 14L0 12.6L5.6 7L0 1.4L1.4 0L7 5.6L12.6 0L14 1.4L8.4 7L14 12.6L12.6 14L7 8.4L1.4 14Z\" fill=\"black\" />\r\n    </svg>\r\n\r\n  )\r\n}\r\n\r\nexport const AddPlusIcon = ({ width = 10, height = 10 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\" width={width} height={height} alt=\"Add Model Icon\" />\r\n\r\n  );\r\n};\r\nexport const DragDropIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drag-drop-icon.svg\" alt=\"Drap Drop Icon\" />\r\n\r\n  );\r\n};\r\nexport const SolidIncon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg\" alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\nexport const BlackDownIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-down-arrow.svg\" alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\n\r\nexport const YellowInfoHexa = () => {\r\n  return (\r\n    <svg\r\n      width=\"12\"\r\n      height=\"14\"\r\n      viewBox=\"0 0 12 14\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M3.22867 1.53727C4.58133 0.736604 5.25733 0.335938 6 0.335938C6.74267 0.335938 7.41867 0.735938 8.77133 1.53727L9.22867 1.80794C10.5813 2.60927 11.2573 3.00994 11.6287 3.66927C12 4.32927 12 5.12927 12 6.73194V7.27327C12 8.87527 12 9.6766 11.6287 10.3359C11.2573 10.9959 10.5813 11.3959 9.22867 12.1966L8.77133 12.4679C7.41867 13.2686 6.74267 13.6693 6 13.6693C5.25733 13.6693 4.58133 13.2693 3.22867 12.4679L2.77133 12.1966C1.41867 11.3966 0.742667 10.9953 0.371333 10.3359C-3.97364e-08 9.67594 0 8.87594 0 7.27327V6.73194C0 5.12927 -3.97364e-08 4.3286 0.371333 3.66927C0.742667 3.00927 1.41867 2.60927 2.77133 1.80794L3.22867 1.53727ZM6.66667 9.66927C6.66667 9.84608 6.59643 10.0157 6.4714 10.1407C6.34638 10.2657 6.17681 10.3359 6 10.3359C5.82319 10.3359 5.65362 10.2657 5.5286 10.1407C5.40357 10.0157 5.33333 9.84608 5.33333 9.66927C5.33333 9.49246 5.40357 9.32289 5.5286 9.19787C5.65362 9.07284 5.82319 9.0026 6 9.0026C6.17681 9.0026 6.34638 9.07284 6.4714 9.19787C6.59643 9.32289 6.66667 9.49246 6.66667 9.66927ZM6 3.16927C6.13261 3.16927 6.25979 3.22195 6.35355 3.31572C6.44732 3.40949 6.5 3.53666 6.5 3.66927V7.66927C6.5 7.80188 6.44732 7.92906 6.35355 8.02282C6.25979 8.11659 6.13261 8.16927 6 8.16927C5.86739 8.16927 5.74022 8.11659 5.64645 8.02282C5.55268 7.92906 5.5 7.80188 5.5 7.66927V3.66927C5.5 3.53666 5.55268 3.40949 5.64645 3.31572C5.74022 3.22195 5.86739 3.16927 6 3.16927Z\" fill=\"#FEA500\" />\r\n    </svg>\r\n\r\n  );\r\n};\r\n\r\nexport const TripleDotsMenu = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kebab-menu.svg\" alt=\"Kabab Menu\" />\r\n  );\r\n};\r\n\r\nexport const WhiteDownArrow = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"13\" height=\"6\" viewBox=\"0 0 14 8\" fill=\"none\">\r\n      <path d=\"M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z\" fill=\"white\" />\r\n    </svg>\r\n    // <img src=\"http://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-down-arrow.svg\" width={width} height={height} alt=\"white down arrow\" />\r\n  );\r\n};\r\nexport const BlackDownArrow = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"13\" height=\"6\" viewBox=\"0 0 14 8\" fill=\"none\">\r\n      <path d=\"M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z\" fill=\"black\" />\r\n    </svg>\r\n  );\r\n};\r\nexport const WhiteCrossIcon = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"15\" viewBox=\"0 0 16 15\" fill=\"none\">\r\n      <path\r\n        d=\"M9.05969 7.49973L15.2847 1.2841C15.4043 1.13838 15.4654 0.953383 15.4562 0.765094C15.4469 0.576804 15.368 0.398686 15.2347 0.265385C15.1014 0.132084 14.9232 0.0531312 14.7349 0.0438836C14.5467 0.0346361 14.3617 0.095755 14.2159 0.215352L8.00031 6.44035L1.78469 0.215352C1.63897 0.095755 1.45397 0.0346361 1.26568 0.0438836C1.07739 0.0531312 0.899272 0.132084 0.76597 0.265385C0.632669 0.398686 0.553717 0.576804 0.544469 0.765094C0.535221 0.953383 0.59634 1.13838 0.715938 1.2841L6.94094 7.49973L0.715938 13.7154C0.575101 13.8575 0.496094 14.0496 0.496094 14.2497C0.496094 14.4499 0.575101 14.6419 0.715938 14.7841C0.859293 14.9227 1.0509 15.0002 1.25031 15.0002C1.44972 15.0002 1.64133 14.9227 1.78469 14.7841L8.00031 8.5591L14.2159 14.7841C14.3593 14.9227 14.5509 15.0002 14.7503 15.0002C14.9497 15.0002 15.1413 14.9227 15.2847 14.7841C15.4255 14.6419 15.5045 14.4499 15.5045 14.2497C15.5045 14.0496 15.4255 13.8575 15.2847 13.7154L9.05969 7.49973Z\"\r\n        fill=\"white\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ProfileUserDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-dark.svg\" alt=\"Profile Dark Icon\" />\r\n  );\r\n};\r\nexport const BlueLocationIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-location.svg\" alt=\"Blue Location Icon\" />\r\n  );\r\n};\r\nexport const RatingStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-star-single.svg\" alt=\"Rating Star Icon\" />\r\n  );\r\n};\r\nexport const ThumbUpIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-up-green.svg\" alt=\"Thumb Up Icon\" />\r\n  );\r\n};\r\nexport const ThumbDownIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-down-red.svg\" alt=\"Thumb Down Icon\" />\r\n  );\r\n};\r\nexport const BlackShareIcon = ({ width = 18, height = 13 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-icon.svg\" width={width} height={height} alt=\"Black Share Icon\" />\r\n  );\r\n};\r\nexport const StaticListingImg = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png\" alt=\"Black Share Icon\" />\r\n  );\r\n};\r\n\r\nexport const WhiteDropDownArrow = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg\" alt=\"White DropDown Icon\" />\r\n  );\r\n};\r\nexport const WhiteSingleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-single-stack.svg\" alt=\"White Single Stack Icon\" />\r\n  );\r\n};\r\nexport const WhiteDoubleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-double-stack.svg\" alt=\"White Double Stack Icon\" />\r\n  );\r\n};\r\nexport const WhiteTripleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-triple-stack.svg\" alt=\"White Triple Stack Icon\" />\r\n  );\r\n};\r\nexport const OpenNewtabIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-open-new-tab.svg\" alt=\"New Tab Icon\" />\r\n  );\r\n};\r\nexport const RenameIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-edit-pencil.svg\" alt=\"Rename Icon\" />\r\n  );\r\n};\r\nexport const DeleteDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trashcan-dark.svg\" alt=\"Delete Dark Icon\" />\r\n  );\r\n};\r\nexport const EyeDarkInsightIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark-insights.svg\" alt=\"Eye Dark Insight Icon\" />\r\n  );\r\n};\r\nexport const EyeDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark.svg\" alt=\"Eye Dark Icon\" />\r\n  );\r\n};\r\nexport const FollowersIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-followers-icon.svg\" alt=\"Followers Icon\" />\r\n  );\r\n};\r\nexport const ShareLightStrIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-white-fill.svg\" alt=\"Followers Icon\" />\r\n  );\r\n};\r\nexport const RelistIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-relist-icon.svg\" alt=\"Relist Icon\" />\r\n  );\r\n};\r\nexport const DigitaLAssetIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-digital-asset.svg\" alt=\"Digital Asset Icon\" />\r\n  );\r\n};\r\nexport const LicenseIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-license.svg\" alt=\"license Icon\" />\r\n  );\r\n};\r\nexport const LightEyeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-light.svg\" alt=\"Light Eye Icon\" />\r\n  );\r\n};\r\nexport const AddBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-add.svg\" alt=\"Add Blue Icon\" />\r\n  );\r\n};\r\nexport const PinIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-pin.svg\" alt=\"Pin Icon\" />\r\n  );\r\n};\r\n\r\nexport const RightArrowIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"8\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 8 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M7.15694 7.21102L1.49994 12.868L0.0859375 11.454L5.03594 6.50401L0.0859375 1.55401L1.49994 0.140015L7.15694 5.79701C7.34441 5.98454 7.44972 6.23885 7.44972 6.50401C7.44972 6.76918 7.34441 7.02349 7.15694 7.21102Z\"\r\n        fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const EditIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M17.71 4.04C18.1 3.65 18.1 3 17.71 2.63L15.37 0.289999C15 -0.100001 14.35 -0.100001 13.96 0.289999L12.12 2.12L15.87 5.87M0 14.25V18H3.75L14.81 6.93L11.06 3.18L0 14.25Z\" fill=\"#00ADEF\"></path>\r\n    </svg>\r\n  );\r\n};\r\nexport const PlusIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"33\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 33 32\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M32.5 20H20.5V32H12.5V20H0.5V12H12.5V0H20.5V12H32.5V20Z\" fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const RemoveIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"19\"\r\n      viewBox=\"0 0 18 19\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z\"\r\n        fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const RightSolidArrowIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"12\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 12 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M10.9903 5.63999L1.94025 0.769994C1.18025 0.359994 0.300251 1.06999 0.540251 1.89999L1.78025 6.23999C1.83025 6.41999 1.83025 6.59999 1.78025 6.77999L0.540251 11.12C0.300251 11.95 1.18025 12.66 1.94025 12.25L10.9903 7.37999C11.1446 7.29563 11.2735 7.17127 11.3632 7.01995C11.453 6.86862 11.5004 6.69593 11.5004 6.51999C11.5004 6.34406 11.453 6.17136 11.3632 6.02004C11.2735 5.86872 11.1446 5.74435 10.9903 5.65999V5.63999Z\"\r\n        fill=\"#00ADEF\"\r\n      >\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const LocationIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"20\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 20 20\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M10.0004 11.1912C11.4363 11.1912 12.6004 10.0272 12.6004 8.59121C12.6004 7.15527 11.4363 5.99121 10.0004 5.99121C8.56445 5.99121 7.40039 7.15527 7.40039 8.59121C7.40039 10.0272 8.56445 11.1912 10.0004 11.1912Z\"\r\n        stroke=\"#292D32\"\r\n        stroke-width=\"1.5\"\r\n      />\r\n      <path\r\n        d=\"M3.01675 7.07533C4.65842 -0.141339 15.3501 -0.133006 16.9834 7.08366C17.9417 11.317 15.3084 14.9003 13.0001 17.117C11.3251 18.7337 8.67508 18.7337 6.99175 17.117C4.69175 14.9003 2.05842 11.3087 3.01675 7.07533Z\"\r\n        stroke=\"#292D32\"\r\n        strokeWidth=\"1.5\"\r\n      />\r\n    </svg>\r\n\r\n  );\r\n};\r\n\r\nexport const BulletPointIcon = ({ width = 8, height = 8 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-bullet-point.svg\" width={width} height={height} alt=\"Bullet Point Icon\" />\r\n  );\r\n};\r\nexport const CoinWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coins-white.svg\" alt=\"White Coin Icon\" />\r\n  );\r\n};\r\nexport const ProductFormatWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-product-format-white.svg\" alt=\"Product Format White Icon\" />\r\n  );\r\n};\r\nexport const GuageWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge-white.svg\" alt=\"Guage White Icon\" />\r\n  );\r\n};\r\nexport const TradingPlatformWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trading-platform-white.svg\" alt=\"Trading Platform White Icon\" />\r\n  );\r\n};\r\nexport const ShuffleWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-shuffle-white.svg\" alt=\"Shuffle White Icon\" />\r\n  );\r\n};\r\nexport const ClockWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-clock-white.svg\" alt=\"Clock White Icon\" />\r\n  );\r\n};\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAM,UAAU;IACrB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,WAAW,kBACtB,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,8OAAC;YACC,IAAG;YACH,aAAU;YACV,GAAE;YACF,WAAU;YACV,MAAK;;;;;;;;;;;AAIJ,MAAM,YAAY;IACvB,qBACE,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,8OAAC;YACC,IAAG;YACH,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC;gBACC,IAAG;gBACH,aAAU;gBACV,WAAU;;kCAEV,8OAAC;wBACC,IAAG;wBACH,aAAU;wBACV,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,WAAU;wBACV,MAAK;;;;;;kCAEP,8OAAC;wBACC,IAAG;wBACH,aAAU;wBACV,WAAU;;0CAEV,8OAAC;gCACC,IAAG;gCACH,aAAU;gCACV,GAAE;gCACF,WAAU;gCACV,MAAK;gCACL,UAAS;;;;;;0CAEX,8OAAC;gCACC,IAAG;gCACH,aAAU;gCACV,GAAE;gCACF,MAAK;gCACL,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAAkF,KAAI;;;;;;AAEnG;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACrD,qBACE,8OAAC;QAAI,KAAI;QAAyE,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAExH;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;AACO,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE;IACrC,qBACE,8OAAC;QACC,KAAI;QACJ,KAAI;QACJ,WAAW;;;;;;AAGjB;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,UAAU;IACrB,qBACE,8OAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,gBAAgB;IAC3B,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,4jCAA4jC;IAC5jC,mBAAmB;IACnB,OAAO;IACP,SAAS;kBACT,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;QACnF,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,YAAY,CAAC,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE;IACnD,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAO;QACP,QAAQ;;;;;;AAGd;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE;IACzD,qBACE,8OAAC;QAAI,KAAI;QAAuE,KAAI;QAClF,OAAO;QACP,QAAQ;QACR,WAAW;;;;;;AAGjB;AACO,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAM,EAAE,SAAS,KAAK,EAAE;IAC1D,qBACE,8OAAC;QAAI,KAAI;QAAwE,QAAQ;QAAQ,OAAO;QAAO,KAAI;;;;;;AAEvH;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;AACO,MAAM,qBAAqB;IAChC,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;QAC9F,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;kBACL,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;AACO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,MAAM,EAAE,SAAS,MAAM,EAAE;IAC/D,qBACE,8OAAC;QAAI,KAAI;QAA8E,QAAQ;QAAQ,OAAO;QAAO,KAAI;;;;;;AAE7H;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE;IACtC,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;QACzF,WAAW;;;;;;AAGjB;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAAuE,KAAI;;;;;;AAExF;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AAEO,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE;IACjC,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,sSAAsS;IACtS,mBAAmB;IACnB,OAAO;IACP,SAAS;kBACT,8OAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;QAEP,WAAW;;;;;;AAGjB;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAI,KAAI;QAAqF,KAAI;;;;;;AAEtG;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,yBAAyB;IACpC,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;AACO,MAAM,uBAAuB;IAClC,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QAAI,KAAI;QAAuE,KAAI;;;;;;AAExF;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;0BACC,cAAA,8OAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,eAAc;oBACd,mBAAkB;;sCAElB,8OAAC;4BAAK,WAAU;;;;;;sCAChB,8OAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKrC;AACO,MAAM,iBAAiB;IAC5B,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,o2BAAo2B;IACp2B,qBAAqB;IACrB,OAAO;IACP,SAAS;kBACT,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACvD,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACzD,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,OAAO;YAAE,WAAW;QAAiB;kBAErC,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;IAC1D,qBACE,8OAAC;QAAI,OAAO;QACV,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBACN,cAAA,8OAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAInF;AACO,MAAM,yBAAyB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;IAC/D,qBACE,8OAAC;QAAI,OAAO;QACV,QAAQ;QACR,SAAQ;QAAW,MAAK;QAAO,OAAM;kBACrC,cAAA,8OAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAGnF;AAEO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAAyE,KAAI;QACpF,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;0BAEhB,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;0BAEhB,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;;;;;;;AAItB;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;QAC1F,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QACC,IAAG;QACH,aAAU;QACV,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,iBAAiB,CAAC,EAAE,UAAU,EAAE;IAC3C,qBACE,8OAAC;QACC,WAAW,aAAa,WAAW;QACnC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,yBAAyB;IACpC,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAMb;AACO,MAAM,iBAAiB;IAC5B,OACE,OAAO;IACP,uCAAuC;IACvC,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,mBAAmB;IACnB,IAAI;IACJ,UAAU;IACV,yHAAyH;IACzH,oBAAoB;IACpB,OAAO;IACP,SAAS;kBACT,8OAAC;QAAI,KAAI;QACP,KAAI;QACJ,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAKb;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;QAC3F,OAAM;QACN,QAAO;;;;;;AAIb;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAG/F;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAG/F;AACO,MAAM,uBAAuB;IAClC,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,uBAAuB;IAClC,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AAEO,MAAM,qBAAqB;IAChC,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AAEO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,OAAM;kBAChE,cAAA,8OAAC;YAAK,GAAE;YAAwL,MAAK;;;;;;;;;;;AAI3M;AACO,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACpD,qBACE,8OAAC;QAAI,OAAO;QAAO,QAAQ;QAAQ,SAAQ;QAAY,MAAK;QAAO,OAAM;kBACvE,cAAA,8OAAC;YAAK,GAAE;YAA2F,MAAK;;;;;;;;;;;AAI9G;AAEO,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACrD,qBACE,8OAAC;QAAI,KAAI;QAAuE,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAGtH;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAGlG;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;AAEO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YAAK,UAAS;YAAU,UAAS;YAAU,GAAE;YAAm3C,MAAK;;;;;;;;;;;AAI56C;AAEO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AAEO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAI,SAAQ;QAAW,MAAK;kBACpF,cAAA,8OAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAInF;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAI,SAAQ;QAAW,MAAK;kBACpF,cAAA,8OAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAGnF;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAGb;AAEO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;AACO,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACxD,qBACE,8OAAC;QAAI,KAAI;QAA6E,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAE5H;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AAEO,MAAM,qBAAqB;IAChC,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,qBAAqB;IAChC,qBACE,8OAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;AACO,MAAM,UAAU;IACrB,qBACE,8OAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;AAEO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YAAK,UAAS;YACb,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YAA0K,MAAK;;;;;;;;;;;AAGzL;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YAA0D,MAAK;;;;;;;;;;;AAIzE;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,yBAAyB;IACpC,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAKb;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,gBAAa;;;;;;0BAEf,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;;;;;;;AAKpB;AAEO,MAAM,kBAAkB,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE;IACvD,qBACE,8OAAC;QAAI,KAAI;QAA+E,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAE9H;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,yBAAyB;IACpC,qBACE,8OAAC;QAAI,KAAI;QAAuF,KAAI;;;;;;AAExG;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,2BAA2B;IACtC,qBACE,8OAAC;QAAI,KAAI;QAAyF,KAAI;;;;;;AAE1G;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F", "debugId": null}}, {"offset": {"line": 2157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/constants/index.js"], "sourcesContent": ["export const placeHolderImg = \"/images/tradereply-placeholder.jpg\";\r\nexport const blogLimit = 300;\r\n\r\nexport const SYSTEM_ROLES = {\r\n  ADMIN: \"admin\",\r\n  USER: \"user\",\r\n  SUPER_ADMIN: \"Super admin\"\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,iBAAiB;AACvB,MAAM,YAAY;AAElB,MAAM,eAAe;IAC1B,OAAO;IACP,MAAM;IACN,aAAa;AACf", "debugId": null}}, {"offset": {"line": 2173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/AuthOverlayMessage.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Cookies from \"js-cookie\";\r\n\r\nexport default function AuthOverlayMessage({ isLoggedIn, Overlay }) {\r\n\r\n    const [loginToken, SetLoginToken] = useState(isLoggedIn);\r\n\r\n    useEffect(() => {\r\n        SetLoginToken(isLoggedIn);\r\n    }, [isLoggedIn]);\r\n\r\n    return (\r\n        <>\r\n            {!loginToken && (\r\n                <div className=\"auth-overlay-container\">\r\n                    <div className={`auth-overlay-message px-3 ${Overlay}`}>\r\n                        <p>\r\n                            Unlock powerful trading tools.{\" \"}\r\n                            <Link href=\"/login\">Log in</Link> or{\" \"}\r\n                            <Link href=\"/signup\">Sign up</Link> for full access.\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS,mBAAmB,EAAE,UAAU,EAAE,OAAO,EAAE;IAE9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,cAAc;IAClB,GAAG;QAAC;KAAW;IAEf,qBACI;kBACK,CAAC,4BACE,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAW,CAAC,0BAA0B,EAAE,SAAS;0BAClD,cAAA,8OAAC;;wBAAE;wBACgC;sCAC/B,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAS;;;;;;wBAAa;wBAAI;sCACrC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAU;;;;;;wBAAc;;;;;;;;;;;;;;;;;;AAO/D", "debugId": null}}, {"offset": {"line": 2241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/LoadingSpinner.js"], "sourcesContent": ["import React from 'react'\r\nimport \"../../css/common/LoadingSpinnter.scss\"\r\n\r\nexport default function LoadingSpinner() {\r\n    return (\r\n        <>\r\n            <div className=\"loading-spinner\">\r\n                <span className=\"customLoader\"></span>\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGe,SAAS;IACpB,qBACI;kBACI,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAK,WAAU;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 2271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/Dashboard/SideBar.jsx"], "sourcesContent": ["\"use client\";\r\nimport NavLink from \"@/Components/UI/NavLink\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  CalculatorIcon,\r\n  PlusIcon,\r\n  RightArrowIcon\r\n} from \"@/assets/svgIcons/SvgIcon\";\r\nimport { useEffect, useRef, useState, useLayoutEffect } from \"react\";\r\nimport \"../../../css/dashboard/Sidebar.scss\";\r\nimport { SYSTEM_ROLES } from \"@/constants\";\r\nimport Cookies from \"js-cookie\";\r\nimport AuthOverlayMessage from \"../AuthOverlayMessage\";\r\nimport LoadingSpinner from \"../LoadingSpinner\";\r\n\r\nconst Sidebar = () => {\r\n  const sliderRef = useRef(null);\r\n  const [disableLeft, setDisableLeft] = useState(true);\r\n  const [disableRight, setDisableRight] = useState(false);\r\n  const [user, setUser] = useState(null);\r\n\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const [loginToken, setLoginToken] = useState(undefined); // Initially undefined\r\n\r\n  useEffect(() => {\r\n    const tokens = Cookies.get(\"authToken\");\r\n    setLoginToken(tokens || null); // Set token once fetched\r\n\r\n    const storedUser = localStorage.getItem(\"user\");\r\n    if (storedUser) {\r\n      setUser(JSON.parse(storedUser));\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const checkScreenSize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n      checkScrollPosition();\r\n    };\r\n\r\n    checkScreenSize();\r\n    window.addEventListener(\"resize\", checkScreenSize);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"resize\", checkScreenSize);\r\n    };\r\n  }, []);\r\n  useLayoutEffect(() => {\r\n    checkScrollPosition();\r\n  }, []);\r\n\r\n  const smoothScroll = (amount) => {\r\n    if (sliderRef.current) {\r\n      const start = sliderRef.current.scrollLeft;\r\n      const end = start + amount;\r\n      const duration = 300;\r\n      const startTime = performance.now();\r\n\r\n      const step = (currentTime) => {\r\n        const elapsed = currentTime - startTime;\r\n        const progress = Math.min(elapsed / duration, 1);\r\n        const scrollAmount = start + (end - start) * progress;\r\n\r\n        sliderRef.current.scrollLeft = scrollAmount;\r\n\r\n        if (progress < 1) {\r\n          requestAnimationFrame(step);\r\n        } else {\r\n          checkScrollPosition(); // <-- Update buttons after scroll\r\n        }\r\n      };\r\n      requestAnimationFrame(step);\r\n    }\r\n  };\r\n\r\n  const scrollLeft = () => {\r\n    if (isMobile) {\r\n      smoothScroll(-260);\r\n    }\r\n  };\r\n\r\n  const scrollRight = () => {\r\n    if (isMobile) {\r\n      smoothScroll(260);\r\n    }\r\n  };\r\n  const checkScrollPosition = () => {\r\n    if (!sliderRef.current) return;\r\n\r\n    const { scrollLeft, scrollWidth, clientWidth } = sliderRef.current;\r\n\r\n    const isAtStart = scrollLeft <= 0;\r\n    const isAtEnd = scrollLeft + clientWidth >= scrollWidth - 1;\r\n\r\n    setDisableLeft(isAtStart);\r\n    setDisableRight(isAtEnd);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const slider = sliderRef.current;\r\n    if (!slider) return;\r\n\r\n    slider.addEventListener(\"scroll\", checkScrollPosition);\r\n    window.addEventListener(\"resize\", checkScrollPosition);\r\n\r\n    checkScrollPosition();\r\n\r\n    return () => {\r\n      slider.removeEventListener(\"scroll\", checkScrollPosition);\r\n      window.removeEventListener(\"resize\", checkScrollPosition);\r\n    };\r\n  }, []);\r\n  return (\r\n    <>\r\n      <div className=\"admin_sidebar\">\r\n        {loginToken === undefined ? (\r\n          <LoadingSpinner />\r\n        ) : (\r\n          <>\r\n            <div className={`${!loginToken ? \"auth-blur-effect\" : \"w-100\"}`}>\r\n              <button\r\n                className={`scroll-btn left ${disableLeft ? \"disabled\" : \"\"}`}\r\n                disabled={disableLeft}\r\n                onClick={scrollLeft}\r\n              >\r\n                <RightArrowIcon />\r\n              </button>\r\n              <div className=\"admin_sidebar_wrapper\" ref={sliderRef}>\r\n                {user?.role === \"Super admin\" ? (\r\n                  superAdminRoutes.map((item) => (\r\n                    <div className=\"linkList\" key={item.path}>\r\n                      <NavLink href={item.path}>\r\n                        <span className=\"linktext\">{item.name.slice(0, 1)}</span>\r\n                        <span className=\"fulltext\"> {item.name}</span>\r\n                      </NavLink>\r\n                    </div>\r\n                  ))\r\n                ) : (\r\n                  <>\r\n                    <div className=\"linkList\">\r\n                      <NavLink href=\"/dashboard/portfolio-manager\">\r\n                        <span className=\"linktext\">\r\n                          <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-portfolio-manager-coin.svg\" alt=\"Portfolio Manager Icon\" />\r\n                        </span>\r\n                        <span className=\"fulltext\">Portfolio Manager</span>\r\n                      </NavLink>\r\n                    </div>\r\n                    <div className=\"linkList\">\r\n                      <NavLink href=\"/dashboard/trade-manager\">\r\n                        <span className=\"linktext\">\r\n                          <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trade-manager-icon.svg\" alt=\"Trade Manager Icon\" />\r\n                        </span>\r\n                        <span className=\"fulltext\">Trade Manager</span>\r\n                      </NavLink>\r\n                    </div>\r\n                    <div className=\"linkList\">\r\n                      <NavLink href=\"/dashboard/strategy-manager\">\r\n                        <span className=\"linktext\">\r\n                          <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-strategy-icon.svg\" alt=\"Strategy Manager Icon\" />\r\n                        </span>\r\n                        <span className=\"fulltext\">Strategy Manager</span>\r\n                      </NavLink>\r\n                    </div>\r\n                    <div className=\"linkList\">\r\n                      <NavLink href=\"/dashboard/tag-manager\">\r\n                        <span className=\"linktext\">\r\n                          <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-tag-manager-icon.svg\" alt=\"Tag Manager Icon\" />\r\n                        </span>\r\n                        <span className=\"fulltext\">Tag Manager</span>\r\n                      </NavLink>\r\n                    </div>\r\n                    <div className=\"linkList\">\r\n                      <NavLink href=\"/dashboard/trade-analysis\">\r\n                        <span className=\"linktext\">\r\n                          <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-analysis-icon.svg\" alt=\"Trade Analysis Icon\" />\r\n                        </span>\r\n                        <span className=\"fulltext\">Trade Analysis</span>\r\n                      </NavLink>\r\n                    </div>\r\n                    <div className=\"linkList\">\r\n                      <NavLink href=\"/dashboard/trade-replay\">\r\n                        <span className=\"linktext\">\r\n                          <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-replay-icon.svg\" alt=\"Trade Replay Icon\" />\r\n                        </span>\r\n                        <span className=\"fulltext\">Trade Replay</span>\r\n                      </NavLink>\r\n                    </div>\r\n                    <div className=\"linkList\">\r\n                      <NavLink href=\"/dashboard/trading-calculator\">\r\n                        <span className=\"linktext\">\r\n                          <CalculatorIcon />\r\n                        </span>\r\n                        <span className=\"fulltext\">Trading Calculators</span>\r\n                      </NavLink>\r\n                    </div>\r\n                    {user?.role == \"Super admin\" && (\r\n                      <>\r\n                        <div className=\"linkList\">\r\n                          <NavLink href=\"/category\">\r\n                            <span className=\"linktext\">\r\n                              <CalculatorIcon />\r\n                            </span>\r\n                            <span className=\"fulltext\">Category</span>\r\n                          </NavLink>\r\n                        </div>\r\n                        <div className=\"linkList\">\r\n                          <NavLink href=\"/education\">\r\n                            <span className=\"linktext\">\r\n                              <CalculatorIcon />\r\n                            </span>\r\n                            <span className=\"fulltext\">Education</span>\r\n                          </NavLink>\r\n                        </div>\r\n                        <div className=\"linkList\">\r\n                          <NavLink href=\"/blogs\">\r\n                            <span className=\"linktext\">\r\n                              <CalculatorIcon />\r\n                            </span>\r\n                            <span className=\"fulltext\">Blogs</span>\r\n                          </NavLink>\r\n                        </div>\r\n                      </>\r\n                    )}\r\n                    <div className=\"linkList\">\r\n                      <NavLink href=\"/dashboard\">\r\n                        <span className=\"linktext\">1</span>\r\n                        <span className=\"fulltext\"> Dashboard1</span>\r\n                      </NavLink>\r\n                    </div>\r\n                    <div className=\"linkList\">\r\n                      <NavLink href=\"#\">\r\n                        <span className=\"linktext\">\r\n                          <PlusIcon />\r\n                        </span>\r\n                        <span className=\"fulltext\">Add Dashboard</span>\r\n                      </NavLink>\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </div>\r\n              <button\r\n                className={`scroll-btn right ${disableRight ? \"disabled\" : \"\"}`}\r\n                disabled={disableRight}\r\n                onClick={scrollRight}\r\n              >\r\n                <RightArrowIcon />\r\n              </button>\r\n            </div>\r\n            <AuthOverlayMessage isLoggedIn={loginToken} />\r\n          </>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nconst superAdminRoutes = [\r\n  // Admin Routes\r\n  {\r\n    name: \"Blog\",\r\n    path: \"/super-admin/blogs\",\r\n    role: SYSTEM_ROLES.SUPER_ADMIN\r\n  },\r\n  {\r\n    name: \"Education\",\r\n    path: \"/super-admin/education\",\r\n    role: SYSTEM_ROLES.SUPER_ADMIN\r\n  },\r\n  {\r\n    name: \"Category\",\r\n    path: \"/super-admin/category\",\r\n    role: SYSTEM_ROLES.SUPER_ADMIN\r\n  }\r\n];\r\n\r\nexport default Sidebar;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAKA;AAEA;AACA;AACA;AACA;AAbA;;;;;;;;;;;AAeA,MAAM,UAAU;IACd,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,sBAAsB;IAE/E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC3B,cAAc,UAAU,OAAO,yBAAyB;QAExD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,QAAQ,KAAK,KAAK,CAAC;QACrB;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,YAAY,OAAO,UAAU,IAAI;YACjC;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE;QACd;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,QAAQ,UAAU,OAAO,CAAC,UAAU;YAC1C,MAAM,MAAM,QAAQ;YACpB,MAAM,WAAW;YACjB,MAAM,YAAY,YAAY,GAAG;YAEjC,MAAM,OAAO,CAAC;gBACZ,MAAM,UAAU,cAAc;gBAC9B,MAAM,WAAW,KAAK,GAAG,CAAC,UAAU,UAAU;gBAC9C,MAAM,eAAe,QAAQ,CAAC,MAAM,KAAK,IAAI;gBAE7C,UAAU,OAAO,CAAC,UAAU,GAAG;gBAE/B,IAAI,WAAW,GAAG;oBAChB,sBAAsB;gBACxB,OAAO;oBACL,uBAAuB,kCAAkC;gBAC3D;YACF;YACA,sBAAsB;QACxB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,UAAU;YACZ,aAAa,CAAC;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU;YACZ,aAAa;QACf;IACF;IACA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,UAAU,OAAO;QAElE,MAAM,YAAY,cAAc;QAChC,MAAM,UAAU,aAAa,eAAe,cAAc;QAE1D,eAAe;QACf,gBAAgB;IAClB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,UAAU;QAElC;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IACL,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;sBACZ,eAAe,0BACd,8OAAC,6IAAA,CAAA,UAAc;;;;yEAEf;;kCACE,8OAAC;wBAAI,WAAW,GAAG,CAAC,aAAa,qBAAqB,SAAS;;0CAC7D,8OAAC;gCACC,WAAW,CAAC,gBAAgB,EAAE,cAAc,aAAa,IAAI;gCAC7D,UAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;gCAAwB,KAAK;0CACzC,MAAM,SAAS,gBACd,iBAAiB,GAAG,CAAC,CAAC,qBACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;4CAAC,MAAM,KAAK,IAAI;;8DACtB,8OAAC;oDAAK,WAAU;8DAAY,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG;;;;;;8DAC/C,8OAAC;oDAAK,WAAU;;wDAAW;wDAAE,KAAK,IAAI;;;;;;;;;;;;;uCAHX,KAAK,IAAI;;;;kGAQ1C;;sDACE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gDAAC,MAAK;;kEACZ,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC;4DAAI,KAAI;4DAAyF,KAAI;;;;;;;;;;;kEAExG,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gDAAC,MAAK;;kEACZ,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC;4DAAI,KAAI;4DAAqF,KAAI;;;;;;;;;;;kEAEpG,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gDAAC,MAAK;;kEACZ,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC;4DAAI,KAAI;4DAAgF,KAAI;;;;;;;;;;;kEAE/F,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gDAAC,MAAK;;kEACZ,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC;4DAAI,KAAI;4DAAmF,KAAI;;;;;;;;;;;kEAElG,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gDAAC,MAAK;;kEACZ,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC;4DAAI,KAAI;4DAAgF,KAAI;;;;;;;;;;;kEAE/F,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gDAAC,MAAK;;kEACZ,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC;4DAAI,KAAI;4DAA8E,KAAI;;;;;;;;;;;kEAE7F,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gDAAC,MAAK;;kEACZ,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;kEAEjB,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;;;;;;wCAG9B,MAAM,QAAQ,+BACb;;8DACE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;wDAAC,MAAK;;0EACZ,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;0EAEjB,8OAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;;;;;;;8DAG/B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;wDAAC,MAAK;;0EACZ,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;0EAEjB,8OAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;;;;;;;8DAG/B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;wDAAC,MAAK;;0EACZ,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;0EAEjB,8OAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;;;;;;;;;sDAKnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gDAAC,MAAK;;kEACZ,8OAAC;wDAAK,WAAU;kEAAW;;;;;;kEAC3B,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gDAAC,MAAK;;kEACZ,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC,oIAAA,CAAA,WAAQ;;;;;;;;;;kEAEX,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;0CAMrC,8OAAC;gCACC,WAAW,CAAC,iBAAiB,EAAE,eAAe,aAAa,IAAI;gCAC/D,UAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;kCAGnB,8OAAC,kJAAA,CAAA,UAAkB;wBAAC,YAAY;;;;;;;;;;;;;;AAM5C;AAEA,MAAM,mBAAmB;IACvB,eAAe;IACf;QACE,MAAM;QACN,MAAM;QACN,MAAM,yHAAA,CAAA,eAAY,CAAC,WAAW;IAChC;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yHAAA,CAAA,eAAY,CAAC,WAAW;IAChC;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yHAAA,CAAA,eAAY,CAAC,WAAW;IAChC;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 2953, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/CommonButton.jsx"], "sourcesContent": ["import \"../../css/common/CommonButton.scss\";\r\n\r\n/**COMMON BUTTON WITH DYNAMIC PROPS */\r\n/** COMMON BUTTON WITH DYNAMIC PROPS */\r\nconst CommonButton = (props) => {\r\n\r\n  return (\r\n    <button\r\n      \r\n      onClick={props?.onClick}\r\n      type={props?.type}\r\n      className={`btn-style ${props.className} ${props.fluid ? \"w-100\" : \"\"} ${props.transparent ? \"transparent\" : \"\"} ${props.white20 ? \"white20\" : \"\"} ${props.whiteBtn ? \"white-btn\" : \"\"}`}\r\n      disabled={props?.disabled}\r\n    >\r\n      {props.onlyIcon && <span className=\"onlyIcon\">{props.onlyIcon}</span>}\r\n\r\n      <div className=\"d-flex flex-column align-items-center text-center\">\r\n        <span>{props.title}</span>\r\n        <span className=\"d-block\">{props.trial}</span>\r\n        <span className=\"d-block\">{props.subtitle}</span>\r\n        {props.innerText && (\r\n          <span style={{ fontSize: \"0.70em\", lineHeight: \"1\" }}>{props.innerText}</span>\r\n        )}\r\n      </div>\r\n\r\n      {props.btnIcon && (\r\n        <img\r\n          src={props.btnIcon}\r\n          alt={props?.title ? `${props.title} icon` : \"Button icon\"}\r\n          className=\"\"\r\n        />\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default CommonButton;"], "names": [], "mappings": ";;;;;;AAEA,oCAAoC,GACpC,qCAAqC,GACrC,MAAM,eAAe,CAAC;IAEpB,qBACE,8OAAC;QAEC,SAAS,OAAO;QAChB,MAAM,OAAO;QACb,WAAW,CAAC,UAAU,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,KAAK,GAAG,UAAU,GAAG,CAAC,EAAE,MAAM,WAAW,GAAG,gBAAgB,GAAG,CAAC,EAAE,MAAM,OAAO,GAAG,YAAY,GAAG,CAAC,EAAE,MAAM,QAAQ,GAAG,cAAc,IAAI;QACxL,UAAU,OAAO;;YAEhB,MAAM,QAAQ,kBAAI,8OAAC;gBAAK,WAAU;0BAAY,MAAM,QAAQ;;;;;;0BAE7D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAM,MAAM,KAAK;;;;;;kCAClB,8OAAC;wBAAK,WAAU;kCAAW,MAAM,KAAK;;;;;;kCACtC,8OAAC;wBAAK,WAAU;kCAAW,MAAM,QAAQ;;;;;;oBACxC,MAAM,SAAS,kBACd,8OAAC;wBAAK,OAAO;4BAAE,UAAU;4BAAU,YAAY;wBAAI;kCAAI,MAAM,SAAS;;;;;;;;;;;;YAIzE,MAAM,OAAO,kBACZ,8OAAC;gBACC,KAAK,MAAM,OAAO;gBAClB,KAAK,OAAO,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG;gBAC5C,WAAU;;;;;;;;;;;;AAKpB;uCAEe", "debugId": null}}, {"offset": {"line": 3038, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Container, Navbar, Dropdown } from \"react-bootstrap\";\r\nimport { useLanguage } from \"@/context/LanguageContext\";\r\nimport {\r\n  DashboardIcon,\r\n  HelpIcon,\r\n  PartnershipIcon,\r\n  ReferIcon,\r\n  SettingIcon,\r\n  SignoutIcon,\r\n  UserBlackIcon,\r\n  GlobalIcons,\r\n  UserBluekIcon\r\n} from \"@/assets/svgIcons/SvgIcon\";\r\n\r\nimport CommonButton from \"./CommonButton\";\r\nimport NavLink from \"./NavLink\";\r\nimport \"../../css/common/Header.scss\";\r\nimport { isEmpty } from \"lodash\";\r\nimport Link from \"next/link\";\r\nimport Cookies from \"js-cookie\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { logout } from \"@/utils/auth\";\r\n\r\n\r\nconst Header = () => {\r\n  const [loginToken, setLoginToken] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const pathname = usePathname();\r\n  const [isFreeUser, setIsFreeUser] = useState(false);\r\n  const [isPricingPage, setIsPricingPage] = useState(false);\r\n  const isHomePage = pathname === \"/\";\r\n  // const props = usePage();\r\n  const user = {};\r\n  const { language, changeLanguage } = useLanguage();\r\n  // const user = usePage().props.auth.user;\r\n  const signIn = !isEmpty(loginToken);\r\n  const [isActive, setIsActive] = useState(false);\r\n  const [isProductOpen, setIsProductOpen] = useState(false);\r\n  const [isLangOpen, setIsLangOpen] = useState(false);\r\n  const [isOpenLanguage, setIsOpenLanguage] = useState(false);\r\n  const [isDesktop, setIsDesktop] = useState(false);\r\n\r\n\r\n  const ref = useRef();\r\n\r\n  const toggleClass = () => setIsActive((prev) => !prev);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsDesktop(window.innerWidth >= 1200);\r\n    };\r\n\r\n    handleResize();\r\n\r\n    window.addEventListener('resize', handleResize);\r\n\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  const toggleProductDropdown = () => {\r\n    setIsProductOpen((prev) => !prev);\r\n  };\r\n\r\n  const handleProductMouseEnter = () => {\r\n    if (isDesktop) {\r\n      setIsProductOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleProductMouseLeave = () => {\r\n    if (isDesktop) {\r\n      setIsProductOpen(false);\r\n    }\r\n  };\r\n\r\n  const toggleMobileLangDropdown = () => {\r\n    setIsLangOpen((prev) => !prev);\r\n  };\r\n\r\n  const toggleMobileLangEnter = () => {\r\n    if (isDesktop) {\r\n      setIsLangOpen(true);\r\n    }\r\n  }\r\n  const toggleMobileLangLeave = () => {\r\n    if (isDesktop) {\r\n      setIsLangOpen(false);\r\n    }\r\n  }\r\n\r\n  const toggleLanguageDropdown = () => {\r\n    setIsOpenLanguage((prev) => !prev);\r\n  };\r\n\r\n  const handleLanguageMouseEnter = () => setIsOpenLanguage(true);\r\n  const handleLanguageMouseLeave = () => setIsOpenLanguage(false);\r\n\r\n  const handleNavClick = () => {\r\n    if (ref.current && document.body.clientWidth < 1220) {\r\n      ref.current.click();\r\n    }\r\n  };\r\n\r\n  const logoutUser = async () => {\r\n    // First clear all local auth data\r\n    Cookies.remove(\"authToken\");\r\n    sessionStorage.clear();\r\n    localStorage.clear();\r\n    \r\n    // Then call the API to logout on server\r\n    const success = await logout();\r\n    \r\n    // Always redirect to login page\r\n    router.push(\"/login\");\r\n  };\r\n\r\n\r\n\r\n  const renderUserDropdown = () => <UserDropdown signIn={loginToken} />;\r\n\r\n  const lang = [\r\n    {\r\n      lang: \"en\",\r\n      title: \"English\"\r\n    },\r\n    {\r\n      lang: \"fr\",\r\n      title: \"French\"\r\n    },\r\n    {\r\n      lang: \"es\",\r\n      title: \"Español\"\r\n    }\r\n  ];\r\n  const changeLang = (lang) => {\r\n    changeLanguage(lang);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const tokens = Cookies.get(\"authToken\");\r\n    setLoginToken(tokens);\r\n\r\n\r\n    setIsPricingPage(pathname === \"/pricing\");\r\n\r\n\r\n    try {\r\n      const user = JSON.parse(localStorage.getItem(\"user\"));\r\n      if (user?.subscription_id === 1) {\r\n        setIsFreeUser(true);\r\n      }\r\n    } catch (e) {\r\n      console.warn(\"Invalid user in localStorage\");\r\n    }\r\n\r\n    setLoading(false);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isActive) {\r\n      document.body.style.overflow = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"auto\";\r\n    }\r\n  \r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.style.overflow = \"auto\";\r\n    };\r\n  }, [isActive]);\r\n  return (\r\n    <header className={`${isHomePage ? \"home-page\" : \"\"}`}>\r\n      <div className={`siteHeader ${isActive ? \"openmenu\" : \"\"}`}>\r\n        <Navbar expand=\"xl\">\r\n          <Container>\r\n            <div className=\"d-flex align-items-center \">\r\n              <Navbar.Toggle ref={ref} onClick={toggleClass} />\r\n              <NavLink href=\"/\" className=\"brandLogo\">\r\n                {/* <Logo /> */}\r\n                <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg\" alt=\"Brand Logo\" />\r\n              </NavLink>\r\n            </div>\r\n            <Navbar.Collapse className=\"justify-content-center\">\r\n              <div className=\"d-flex justify-content-center align-items-center openmenuSidebar\">\r\n                <NavLink\r\n                  onClick={handleNavClick}\r\n                  href=\"/\"\r\n                  className=\"brandLogo d-block d-xl-none\"\r\n                >\r\n                  {/* <Logo /> */}\r\n                  <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg\" alt=\"Brand Logo\" />\r\n                </NavLink>\r\n                <Navbar.Toggle ref={ref} onClick={toggleClass} />\r\n              </div>\r\n\r\n              <div className=\"navMenu d-xl-flex\">\r\n                <NavLink\r\n                  onClick={handleNavClick}\r\n                  href=\"/marketplace\"\r\n                  className=\"nav-link\"\r\n                >\r\n                  Marketplace\r\n                </NavLink>\r\n\r\n                {/* Product Dropdown */}\r\n                <div\r\n                  className={`nav-item common_dropdown dropdown ${isProductOpen ? \"show\" : \"\"}`}\r\n                  onMouseEnter={handleProductMouseEnter}\r\n                  onMouseLeave={handleProductMouseLeave}\r\n                  onClick={toggleProductDropdown}\r\n                >\r\n                  <NavLink\r\n                    className=\"nav-link dropdown-toggle\"\r\n                    href=\"#\"\r\n                    id=\"navbarDropdown\"\r\n                    role=\"button\"\r\n                    aria-haspopup=\"true\"\r\n                    aria-expanded={isProductOpen ? \"true\" : \"false\"}\r\n                  >\r\n                    Products\r\n                  </NavLink>\r\n\r\n                  <div\r\n                    className={`dropdown-menu ${isProductOpen ? \"show\" : \"\"}`}\r\n                    aria-labelledby=\"navbarDropdown\"\r\n                  >\r\n                    {!isEmpty(loginToken) && (\r\n                      <NavLink onClick={handleNavClick} href=\"/dashboard/trading-calculator\" className=\"nav-link\">\r\n                        Trading Calculator\r\n                      </NavLink>\r\n                    )}\r\n                    {isEmpty(loginToken) && (\r\n                      <NavLink onClick={handleNavClick} href=\"/trading-calculator\" className=\"nav-link\">\r\n                        Trading Calculators\r\n                      </NavLink>\r\n                    )}\r\n                    <NavLink onClick={handleNavClick} href=\"/features\" className=\"nav-link\">\r\n                      Features\r\n                    </NavLink>\r\n                    {loginToken ? (\r\n                      // <Link\r\n                      //   onClick={handleNavClick}\r\n                      //   href={\r\n                      //     isFreeUser ? \"/pricing?source=free_header_menu_pricing&feature=buy_trial\"\r\n                      //       : \"/pricing?source=member_header_menu_pricing&feature=buy_trial\"\r\n                      //   }\r\n                      //   className=\"nav-link\"\r\n\r\n                      // >\r\n                      //   Pricing\r\n                      // </Link>\r\n                      <a\r\n                        href=\"#\"\r\n                        onClick={(e) => {\r\n                          e.preventDefault();\r\n                          handleNavClick(); // keep this if you want the sidebar to close\r\n                          const user = JSON.parse(localStorage.getItem(\"user\"));\r\n                          const isFree = user?.subscription_id === 1;\r\n                          console.log(\"user?.subscription_iddddddddd\",user?.subscription_id);\r\n                          \r\n                          const targetUrl = isFree\r\n                            ? \"/pricing?source=free_header_menu_pricing&feature=buy_trial\"\r\n                            : \"/pricing?source=member_header_menu_pricing&feature=buy_trial\";\r\n                          window.location.href = targetUrl;\r\n                        }}\r\n                        className=\"nav-link\"\r\n                      >\r\n                        Pricing\r\n                      </a>\r\n\r\n                    ) : (\r\n                      <NavLink onClick={handleNavClick} href=\"/pricing\"\r\n                        className=\"nav-link\"\r\n                      >\r\n                        Pricing\r\n                      </NavLink>\r\n                    )}\r\n\r\n\r\n                  </div>\r\n                </div>\r\n\r\n                <NavLink\r\n                  onClick={handleNavClick}\r\n                  href=\"/education\"\r\n                  className=\"nav-link\"\r\n                >\r\n                  Education\r\n                </NavLink>\r\n                <NavLink\r\n                  onClick={handleNavClick}\r\n                  href=\"/blog\"\r\n                  className=\"nav-link\"\r\n                >\r\n                  Blog\r\n                </NavLink>\r\n\r\n                {/* Language Dropdown */}\r\n                <div\r\n                  className={`nav-item common_dropdown dropdown d-block d-xl-none ${isLangOpen ? \"show\" : \"\"}`}\r\n                  onMouseEnter={toggleMobileLangEnter}\r\n                  onMouseLeave={toggleMobileLangLeave}\r\n                  onClick={toggleMobileLangDropdown}\r\n                >\r\n                  <NavLink\r\n                    className=\"nav-link dropdown-toggle\"\r\n                    href=\"#\"\r\n                    id=\"navbarDropdown\"\r\n                    role=\"button\"\r\n                    aria-haspopup=\"true\"\r\n                    aria-expanded={isLangOpen ? \"true\" : \"false\"}\r\n                  >\r\n                    <span className=\"globalIcon\">\r\n                      <GlobalIcons />\r\n                    </span>\r\n                    <p className=\"text-capitalize fs-5 ms-2\">{language}</p>\r\n                  </NavLink>\r\n\r\n                  <div\r\n                    className={`dropdown-menu ${isLangOpen ? \"show\" : \"\"}`}\r\n                    aria-labelledby=\"navbarDropdown\"\r\n                  >\r\n                    {lang.map((Language) => (\r\n                      <NavLink\r\n                        onClick={() => changeLang(Language.lang)}\r\n                        key={Language.lang}\r\n                        href=\"\"\r\n                        className=\"nav-link text-white d-flex flex-column gap-3 fs-5 fw-bold\"\r\n                      >\r\n                        {Language.title}\r\n                      </NavLink>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                <div className='d-block d-xl-none'>\r\n                  {/* <UserDropdown /> */}\r\n                  {signIn ? (\r\n                    <>\r\n                      <Link href=\"\" className='nav-link'><span className='me-3' onClick={() => logoutUser()}><SignoutIcon /></span> Sign Out</Link>\r\n                      {/* <NavLink href=\"/account-overview\" className='dropdown-item white_icon'><SettingIcon /> Account Settings</NavLink> */}\r\n                    </>\r\n                  ) : (\r\n                    <Link href=\"/login\" className='nav-link'><span className='me-3'><SignoutIcon /></span> Log In</Link>\r\n                  )}\r\n\r\n                  <Link href=\"/help\" className='nav-link'><span className='me-3'><HelpIcon /></span> Help Center</Link>\r\n                  <NavLink href=\"/partner\" className='nav-link white_stroke_icon'><span className='me-3'><PartnershipIcon /></span> Partnership</NavLink>\r\n                  <NavLink href=\"/refer-a-friend\" className='nav-link'><span className='me-3'><ReferIcon /></span> Refer A Friend</NavLink>\r\n                </div>\r\n              </div>\r\n            </Navbar.Collapse>\r\n\r\n            {isActive && (\r\n              <div\r\n                onClick={handleNavClick}\r\n                className=\"sidebar_backdrop d-xl-none\"\r\n              />\r\n            )}\r\n            {/* Language Desktop */}\r\n            <div className=\"languageDropdown d-none d-xl-flex\">\r\n              <div\r\n                className={`nav-item common_dropdown dropdown ${isOpenLanguage ? \"show\" : \"\"}`}\r\n                onMouseEnter={handleLanguageMouseEnter}\r\n                onMouseLeave={handleLanguageMouseLeave}\r\n                onClick={toggleLanguageDropdown}\r\n              >\r\n                <NavLink\r\n                  className=\"nav-link dropdown-toggle\"\r\n                  href=\"#\"\r\n                  id=\"navbarDropdown\"\r\n                  role=\"button\"\r\n                  aria-haspopup=\"true\"\r\n                  aria-expanded={isOpenLanguage ? \"true\" : \"false\"}\r\n                >\r\n                  <div className={`globalIcon ${isOpenLanguage ? \"active\" : \"\"}`}>\r\n                    <img\r\n                      src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-global.svg\"\r\n                      alt=\"Global Icon Black\"\r\n                      className=\"icon black\"\r\n                    />\r\n                    <img\r\n                      src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-brand-blue-global.svg\"\r\n                      alt=\"Global Icon Blue\"\r\n                      className=\"icon blue\"\r\n                    />\r\n                  </div>\r\n                  <p className=\"text-capitalize fs-5 ms-2\">{language}</p>\r\n                </NavLink>\r\n\r\n                <div\r\n                  className={`dropdown-menu ${isOpenLanguage ? \"show\" : \"\"}`}\r\n                  aria-labelledby=\"navbarDropdown\"\r\n                >\r\n                  {lang.map((Language) => (\r\n                    <NavLink\r\n                      onClick={() => changeLang(Language.lang)}\r\n                      key={Language.lang}\r\n                      href=\"\"\r\n                      className=\"nav-link text-white d-flex flex-column gap-3 p-2 px-3 fs-5 fw-bold\"\r\n                    >\r\n                      {Language.title}\r\n                    </NavLink>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"mx-2 mx-xl-4 d-none d-xl-block\">\r\n              {renderUserDropdown()}\r\n            </div>\r\n            {!loading && (\r\n              <>\r\n                {!loginToken && pathname !== \"/pricing\" && (\r\n                  <Link href=\"/pricing\">\r\n                    <CommonButton className=\"gradient-btn\" title=\"Get started\" />\r\n                  </Link>\r\n                )}\r\n\r\n                {loginToken && isFreeUser && !isPricingPage && (\r\n                  <Link href=\"/pricing?source=free_header_upgrade_button&feature=buy_trial\">\r\n                    <button className=\"btn-style gradient-btn \">\r\n                      <div className=\"d-flex flex-column align-items-center text-center gap-1\">\r\n                        <span style={{ lineHeight: \"1\" }} >Upgrade Now</span>\r\n                        <span style={{ fontSize: \"0.70em\", lineHeight: \"1\" }}>30-Day Free Trial</span>\r\n                      </div>\r\n                    </button>\r\n                  </Link>\r\n                )}\r\n              </>\r\n            )}\r\n\r\n          </Container>\r\n        </Navbar >\r\n      </div>\r\n    </header >\r\n  );\r\n};\r\n\r\nconst UserDropdown = ({ signIn }) => {\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const logoutUser = async () => {\r\n    // First clear all local auth data\r\n    Cookies.remove(\"authToken\");\r\n    sessionStorage.clear();\r\n    localStorage.clear();\r\n    \r\n    // Then call the API to logout on server\r\n    const success = await logout();\r\n    \r\n    // Always redirect to login page\r\n    router.push(\"/login\");\r\n  };\r\n\r\n\r\n  return (\r\n    <Dropdown\r\n      align=\"end\"\r\n      className=\"common_dropdown userDropdown\"\r\n      show={isUserDropdownOpen}\r\n      onToggle={(isOpen) => setIsUserDropdownOpen(isOpen)}\r\n    >\r\n      <Dropdown.Toggle variant=\"\" id=\"dropdown-basic\">\r\n        <span className=\"user_icon\">\r\n          {isUserDropdownOpen ? <UserBluekIcon /> : <UserBlackIcon />}\r\n        </span>\r\n        <span className=\"user_name\"></span>\r\n      </Dropdown.Toggle>\r\n      <Dropdown.Menu>\r\n        {signIn ? (\r\n          <>\r\n            <NavLink\r\n              href=\"/dashboard\"\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <DashboardIcon color=\"image_color_to_white\" /> Dashboard\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/help\"\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <HelpIcon /> Help Center\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/account/overview\"\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <SettingIcon color=\"image_color_to_white\" /> Account Settings\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/partner\"\r\n              className=\"dropdown-item white_stroke_icon flex items-center\"\r\n            >\r\n              <PartnershipIcon /> Partnership\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/refer-a-friend\"\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <ReferIcon /> Refer A Friend\r\n            </NavLink>\r\n            <NavLink\r\n              href=\"/login\"\r\n              onClick={() => { logoutUser() }}\r\n              className=\"dropdown-item white_icon flex items-center\"\r\n            >\r\n              <SignoutIcon /> Log Out\r\n            </NavLink>\r\n          </>\r\n        ) : (\r\n          <>\r\n            <NavLink href=\"/login\" className=\"dropdown-item d-flex align-items-center\">\r\n              <SignoutIcon /> Log In\r\n            </NavLink>\r\n            <NavLink href=\"/help\" className=\"dropdown-item d-flex align-items-center\">\r\n              <HelpIcon /> Help Center\r\n            </NavLink>\r\n            <NavLink href=\"/partner\" className=\"dropdown-item d-flex align-items-center white_stroke_icon\">\r\n              <PartnershipIcon /> Partnership\r\n            </NavLink>\r\n            <NavLink href=\"/refer-a-friend\" className=\"dropdown-item d-flex align-items-center\">\r\n              <ReferIcon /> Refer A Friend\r\n            </NavLink>\r\n          </>\r\n        )}\r\n      </Dropdown.Menu>\r\n    </Dropdown>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAYA;AACA;AAEA;AACA;AACA;AAEA;AAzBA;;;;;;;;;;;;;;;AA4BA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,aAAa,aAAa;IAChC,2BAA2B;IAC3B,MAAM,OAAO,CAAC;IACd,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IAC/C,0CAA0C;IAC1C,MAAM,SAAS,CAAC,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD,EAAE;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAG3C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEjB,MAAM,cAAc,IAAM,YAAY,CAAC,OAAS,CAAC;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,aAAa,OAAO,UAAU,IAAI;QACpC;QAEA;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,iBAAiB,CAAC,OAAS,CAAC;IAC9B;IAEA,MAAM,0BAA0B;QAC9B,IAAI,WAAW;YACb,iBAAiB;QACnB;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI,WAAW;YACb,iBAAiB;QACnB;IACF;IAEA,MAAM,2BAA2B;QAC/B,cAAc,CAAC,OAAS,CAAC;IAC3B;IAEA,MAAM,wBAAwB;QAC5B,IAAI,WAAW;YACb,cAAc;QAChB;IACF;IACA,MAAM,wBAAwB;QAC5B,IAAI,WAAW;YACb,cAAc;QAChB;IACF;IAEA,MAAM,yBAAyB;QAC7B,kBAAkB,CAAC,OAAS,CAAC;IAC/B;IAEA,MAAM,2BAA2B,IAAM,kBAAkB;IACzD,MAAM,2BAA2B,IAAM,kBAAkB;IAEzD,MAAM,iBAAiB;QACrB,IAAI,IAAI,OAAO,IAAI,SAAS,IAAI,CAAC,WAAW,GAAG,MAAM;YACnD,IAAI,OAAO,CAAC,KAAK;QACnB;IACF;IAEA,MAAM,aAAa;QACjB,kCAAkC;QAClC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,eAAe,KAAK;QACpB,aAAa,KAAK;QAElB,wCAAwC;QACxC,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,SAAM,AAAD;QAE3B,gCAAgC;QAChC,OAAO,IAAI,CAAC;IACd;IAIA,MAAM,qBAAqB,kBAAM,8OAAC;YAAa,QAAQ;;;;;;IAEvD,MAAM,OAAO;QACX;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;QACT;KACD;IACD,MAAM,aAAa,CAAC;QAClB,eAAe;IACjB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC3B,cAAc;QAGd,iBAAiB,aAAa;QAG9B,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;YAC7C,IAAI,MAAM,oBAAoB,GAAG;gBAC/B,cAAc;YAChB;QACF,EAAE,OAAO,GAAG;YACV,QAAQ,IAAI,CAAC;QACf;QAEA,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,qBAAqB;QACrB,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAS;IACb,qBACE,8OAAC;QAAO,WAAW,GAAG,aAAa,cAAc,IAAI;kBACnD,cAAA,8OAAC;YAAI,WAAW,CAAC,WAAW,EAAE,WAAW,aAAa,IAAI;sBACxD,cAAA,8OAAC,wLAAA,CAAA,SAAM;gBAAC,QAAO;0BACb,cAAA,8OAAC,8LAAA,CAAA,YAAS;;sCACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wLAAA,CAAA,SAAM,CAAC,MAAM;oCAAC,KAAK;oCAAK,SAAS;;;;;;8CAClC,8OAAC,mIAAA,CAAA,UAAO;oCAAC,MAAK;oCAAI,WAAU;8CAE1B,cAAA,8OAAC;wCAAI,KAAI;wCAAkF,KAAI;;;;;;;;;;;;;;;;;sCAGnG,8OAAC,wLAAA,CAAA,SAAM,CAAC,QAAQ;4BAAC,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,mIAAA,CAAA,UAAO;4CACN,SAAS;4CACT,MAAK;4CACL,WAAU;sDAGV,cAAA,8OAAC;gDAAI,KAAI;gDAAkF,KAAI;;;;;;;;;;;sDAEjG,8OAAC,wLAAA,CAAA,SAAM,CAAC,MAAM;4CAAC,KAAK;4CAAK,SAAS;;;;;;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,mIAAA,CAAA,UAAO;4CACN,SAAS;4CACT,MAAK;4CACL,WAAU;sDACX;;;;;;sDAKD,8OAAC;4CACC,WAAW,CAAC,kCAAkC,EAAE,gBAAgB,SAAS,IAAI;4CAC7E,cAAc;4CACd,cAAc;4CACd,SAAS;;8DAET,8OAAC,mIAAA,CAAA,UAAO;oDACN,WAAU;oDACV,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,iBAAc;oDACd,iBAAe,gBAAgB,SAAS;8DACzC;;;;;;8DAID,8OAAC;oDACC,WAAW,CAAC,cAAc,EAAE,gBAAgB,SAAS,IAAI;oDACzD,mBAAgB;;wDAEf,CAAC,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD,EAAE,6BACR,8OAAC,mIAAA,CAAA,UAAO;4DAAC,SAAS;4DAAgB,MAAK;4DAAgC,WAAU;sEAAW;;;;;;wDAI7F,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD,EAAE,6BACP,8OAAC,mIAAA,CAAA,UAAO;4DAAC,SAAS;4DAAgB,MAAK;4DAAsB,WAAU;sEAAW;;;;;;sEAIpF,8OAAC,mIAAA,CAAA,UAAO;4DAAC,SAAS;4DAAgB,MAAK;4DAAY,WAAU;sEAAW;;;;;;wDAGvE,aACC,QAAQ;wDACR,6BAA6B;wDAC7B,WAAW;wDACX,gFAAgF;wDAChF,yEAAyE;wDACzE,MAAM;wDACN,yBAAyB;wDAEzB,IAAI;wDACJ,YAAY;wDACZ,UAAU;sEACV,8OAAC;4DACC,MAAK;4DACL,SAAS,CAAC;gEACR,EAAE,cAAc;gEAChB,kBAAkB,6CAA6C;gEAC/D,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;gEAC7C,MAAM,SAAS,MAAM,oBAAoB;gEACzC,QAAQ,GAAG,CAAC,iCAAgC,MAAM;gEAElD,MAAM,YAAY,SACd,+DACA;gEACJ,OAAO,QAAQ,CAAC,IAAI,GAAG;4DACzB;4DACA,WAAU;sEACX;;;;;qHAKD,8OAAC,mIAAA,CAAA,UAAO;4DAAC,SAAS;4DAAgB,MAAK;4DACrC,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDASP,8OAAC,mIAAA,CAAA,UAAO;4CACN,SAAS;4CACT,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,mIAAA,CAAA,UAAO;4CACN,SAAS;4CACT,MAAK;4CACL,WAAU;sDACX;;;;;;sDAKD,8OAAC;4CACC,WAAW,CAAC,oDAAoD,EAAE,aAAa,SAAS,IAAI;4CAC5F,cAAc;4CACd,cAAc;4CACd,SAAS;;8DAET,8OAAC,mIAAA,CAAA,UAAO;oDACN,WAAU;oDACV,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,iBAAc;oDACd,iBAAe,aAAa,SAAS;;sEAErC,8OAAC;4DAAK,WAAU;sEACd,cAAA,8OAAC,oIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAG5C,8OAAC;oDACC,WAAW,CAAC,cAAc,EAAE,aAAa,SAAS,IAAI;oDACtD,mBAAgB;8DAEf,KAAK,GAAG,CAAC,CAAC,yBACT,8OAAC,mIAAA,CAAA,UAAO;4DACN,SAAS,IAAM,WAAW,SAAS,IAAI;4DAEvC,MAAK;4DACL,WAAU;sEAET,SAAS,KAAK;2DAJV,SAAS,IAAI;;;;;;;;;;;;;;;;sDAU1B,8OAAC;4CAAI,WAAU;;gDAEZ,uBACC;8DACE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAG,WAAU;;0EAAW,8OAAC;gEAAK,WAAU;gEAAO,SAAS,IAAM;0EAAc,cAAA,8OAAC,oIAAA,CAAA,cAAW;;;;;;;;;;4DAAU;;;;;;;kFAI/G,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAAW,8OAAC;4DAAK,WAAU;sEAAO,cAAA,8OAAC,oIAAA,CAAA,cAAW;;;;;;;;;;wDAAU;;;;;;;8DAGxF,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAAW,8OAAC;4DAAK,WAAU;sEAAO,cAAA,8OAAC,oIAAA,CAAA,WAAQ;;;;;;;;;;wDAAU;;;;;;;8DAClF,8OAAC,mIAAA,CAAA,UAAO;oDAAC,MAAK;oDAAW,WAAU;;sEAA6B,8OAAC;4DAAK,WAAU;sEAAO,cAAA,8OAAC,oIAAA,CAAA,kBAAe;;;;;;;;;;wDAAU;;;;;;;8DACjH,8OAAC,mIAAA,CAAA,UAAO;oDAAC,MAAK;oDAAkB,WAAU;;sEAAW,8OAAC;4DAAK,WAAU;sEAAO,cAAA,8OAAC,oIAAA,CAAA,YAAS;;;;;;;;;;wDAAU;;;;;;;;;;;;;;;;;;;;;;;;;wBAKrG,0BACC,8OAAC;4BACC,SAAS;4BACT,WAAU;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAW,CAAC,kCAAkC,EAAE,iBAAiB,SAAS,IAAI;gCAC9E,cAAc;gCACd,cAAc;gCACd,SAAS;;kDAET,8OAAC,mIAAA,CAAA,UAAO;wCACN,WAAU;wCACV,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,iBAAc;wCACd,iBAAe,iBAAiB,SAAS;;0DAEzC,8OAAC;gDAAI,WAAW,CAAC,WAAW,EAAE,iBAAiB,WAAW,IAAI;;kEAC5D,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;;0DAGd,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAG5C,8OAAC;wCACC,WAAW,CAAC,cAAc,EAAE,iBAAiB,SAAS,IAAI;wCAC1D,mBAAgB;kDAEf,KAAK,GAAG,CAAC,CAAC,yBACT,8OAAC,mIAAA,CAAA,UAAO;gDACN,SAAS,IAAM,WAAW,SAAS,IAAI;gDAEvC,MAAK;gDACL,WAAU;0DAET,SAAS,KAAK;+CAJV,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;sCAU5B,8OAAC;4BAAI,WAAU;sCACZ;;;;;;wBAEF,CAAC,yBACA;;gCACG,CAAC,cAAc,aAAa,4BAC3B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,wIAAA,CAAA,UAAY;wCAAC,WAAU;wCAAe,OAAM;;;;;;;;;;;gCAIhD,cAAc,cAAc,CAAC,+BAC5B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,OAAO;wDAAE,YAAY;oDAAI;8DAAI;;;;;;8DACnC,8OAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAU,YAAY;oDAAI;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa9E;AAEA,MAAM,eAAe,CAAC,EAAE,MAAM,EAAE;IAC9B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,UAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa;QACjB,kCAAkC;QAClC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,eAAe,KAAK;QACpB,aAAa,KAAK;QAElB,wCAAwC;QACxC,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,SAAM,AAAD;QAE3B,gCAAgC;QAChC,QAAO,IAAI,CAAC;IACd;IAGA,qBACE,8OAAC,4LAAA,CAAA,WAAQ;QACP,OAAM;QACN,WAAU;QACV,MAAM;QACN,UAAU,CAAC,SAAW,sBAAsB;;0BAE5C,8OAAC,4LAAA,CAAA,WAAQ,CAAC,MAAM;gBAAC,SAAQ;gBAAG,IAAG;;kCAC7B,8OAAC;wBAAK,WAAU;kCACb,mCAAqB,8OAAC,oIAAA,CAAA,gBAAa;;;;qFAAM,8OAAC,oIAAA,CAAA,gBAAa;;;;;;;;;;kCAE1D,8OAAC;wBAAK,WAAU;;;;;;;;;;;;0BAElB,8OAAC,4LAAA,CAAA,WAAQ,CAAC,IAAI;0BACX,uBACC;;sCACE,8OAAC,mIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,8OAAC,oIAAA,CAAA,gBAAa;oCAAC,OAAM;;;;;;gCAAyB;;;;;;;sCAEhD,8OAAC,mIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,8OAAC,oIAAA,CAAA,WAAQ;;;;;gCAAG;;;;;;;sCAEd,8OAAC,mIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,8OAAC,oIAAA,CAAA,cAAW;oCAAC,OAAM;;;;;;gCAAyB;;;;;;;sCAE9C,8OAAC,mIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,8OAAC,oIAAA,CAAA,kBAAe;;;;;gCAAG;;;;;;;sCAErB,8OAAC,mIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,WAAU;;8CAEV,8OAAC,oIAAA,CAAA,YAAS;;;;;gCAAG;;;;;;;sCAEf,8OAAC,mIAAA,CAAA,UAAO;4BACN,MAAK;4BACL,SAAS;gCAAQ;4BAAa;4BAC9B,WAAU;;8CAEV,8OAAC,oIAAA,CAAA,cAAW;;;;;gCAAG;;;;;;;;iDAInB;;sCACE,8OAAC,mIAAA,CAAA,UAAO;4BAAC,MAAK;4BAAS,WAAU;;8CAC/B,8OAAC,oIAAA,CAAA,cAAW;;;;;gCAAG;;;;;;;sCAEjB,8OAAC,mIAAA,CAAA,UAAO;4BAAC,MAAK;4BAAQ,WAAU;;8CAC9B,8OAAC,oIAAA,CAAA,WAAQ;;;;;gCAAG;;;;;;;sCAEd,8OAAC,mIAAA,CAAA,UAAO;4BAAC,MAAK;4BAAW,WAAU;;8CACjC,8OAAC,oIAAA,CAAA,kBAAe;;;;;gCAAG;;;;;;;sCAErB,8OAAC,mIAAA,CAAA,UAAO;4BAAC,MAAK;4BAAkB,WAAU;;8CACxC,8OAAC,oIAAA,CAAA,YAAS;;;;;gCAAG;;;;;;;;;;;;;;;;;;;;AAO3B;uCAEe", "debugId": null}}, {"offset": {"line": 4040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Col, Container, Row } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport Cookies from \"js-cookie\";\r\n// import { Logo } from \"@/assets/svgIcons/SvgIcon\";\r\nimport \"../../css/common/Footer.scss\";\r\nimport { Image } from 'next/image';\r\nimport { useEffect, useState } from \"react\";\r\n\r\nconst Footer = () => {\r\n  const url = usePathname();\r\n  const [loginToken, setLoginToken] = useState(null);\r\n  const [isFreeUser, setIsFreeUser] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const tokens = Cookies.get(\"authToken\");\r\n    setLoginToken(tokens);\r\n\r\n\r\n    try {\r\n      const user = JSON.parse(localStorage.getItem(\"user\"));\r\n      if (user?.subscription_id === 1) {\r\n        setIsFreeUser(true);\r\n      }\r\n    } catch (e) {\r\n      console.warn(\"Invalid user in localStorage\");\r\n    }\r\n\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"site_footer\">\r\n        <div className=\"site_footer_inner\">\r\n          <Container>\r\n            <Row className=\"gx-xl-5\">\r\n              <Col md={4} sm={12} xs={12}>\r\n                <div className=\"site_footer_content\">\r\n                  <div className=\"site_footer_logo\">\r\n                    <Link prefetch={true} href=\"/\">\r\n                      <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg\" alt=\"Brand Logo\" />\r\n                    </Link>\r\n                  </div>\r\n                  <p>\r\n                    TradeReply is an advanced analytics suite designed for\r\n                    crypto and stock traders to input historical trading data\r\n                    and leverage powerful visuals, graphs, and metrics to\r\n                    optimize and develop effective trade strategies with\r\n                    real-time insights.\r\n                  </p>\r\n                </div>\r\n              </Col>\r\n              <Col md={8} sm={12} xs={12}>\r\n                <Row>\r\n                  <Col md={4} sm={4} xs={6}>\r\n                    <div className=\"site_footer_links\">\r\n                      <h4>Company</h4>\r\n                      <ul>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/help/hc/en-us/requests/new\" className={url == '/help/hc/en-us/requests/new' ? 'new-link' : ''}>Contact</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/brand-assets\" className={url == '/brand-assets' ? 'new-link' : ''}>Brand Assets</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/accessibility\" className={url == '/accessibility' ? 'new-link' : ''}>Accessibility</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/privacy\" className={url == '/privacy' ? 'new-link' : ''}>\r\n                            Privacy Policy\r\n                          </Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/cookies\" className={url == '/cookies' ? 'new-link' : ''}>\r\n                            Cookies Policy\r\n                          </Link>\r\n                        </li>\r\n                        <li>\r\n                          <a\r\n                            href=\"#\"\r\n                            onClick={(e) => {\r\n                              e.preventDefault(); // prevents scroll to top\r\n                              if (typeof Osano !== \"undefined\" && Osano.cm) {\r\n                                Osano.cm.showDrawer(\"osano-cm-dom-info-dialog-open\");\r\n                              }\r\n                            }}\r\n                          >\r\n                            Cookie Settings\r\n                          </a>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/terms\" className={url == '/terms' ? 'new-link' : ''}>\r\n                            Terms & Conditions\r\n                          </Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/disclaimer\" className={url == '/disclaimer' ? 'new-link' : ''}>\r\n                            Disclaimer\r\n                          </Link>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  </Col>\r\n\r\n                  <Col md={4} sm={4} xs={6}>\r\n                    <div className=\"site_footer_links\">\r\n                      <h4>Partners</h4>\r\n                      <ul>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/refer-a-friend\" className={url == '/refer-a-friend' ? 'new-link' : ''}>Refer a Friend</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/partner\" className={url == '/partner' ? 'new-link' : ''}>Partner Program</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/advertising\" className={url == '/advertising' ? 'new-link' : ''}>Advertising</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/features\" className={url == '/features' ? 'new-link' : ''}>Features</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/education\" className={url == '/education' ? 'new-link' : ''}>Education</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/brokers\" className={url == '/brokers' ? 'new-link' : ''}>Brokers</Link>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  </Col>\r\n                  <Col md={4} sm={4} xs={6}>\r\n                    <div className=\"site_footer_links\">\r\n                      <h4>Community</h4>\r\n                      <ul>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/help\" className={url == '/helpcenter' ? 'new-link' : ''}>Help Center</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/sitemap\" className={url == '/sitemap' ? 'new-link' : ''}>Sitemap</Link>\r\n                        </li>\r\n                        <li>\r\n                          {/* <Link prefetch={true}\r\n                            href={\r\n                              typeof window !== \"undefined\" && sessionStorage.getItem(\"plan\") === \"Free\"\r\n                                ? \"/pricing?source=free_footer_menu_pricing&feature=buy_trial\"\r\n                                : \"/pricing?source=member_footer_menu_pricing&feature=buy_trial\"\r\n                            } className={url == '/pricing' ? 'new-link' : ''}>Pricing</Link> */}\r\n\r\n                          {loginToken ? (\r\n                           <Link\r\n                             prefetch={true}\r\n                             href={\r\n                               loginToken\r\n                                 ? isFreeUser\r\n                                   ? \"/pricing?source=free_footer_menu_pricing&feature=buy_trial\"\r\n                                   : \"/pricing?source=member_footer_menu_pricing&feature=buy_trial\"\r\n                                 : \"/pricing\"\r\n                             }\r\n                             className={url == \"/pricing\" ? \"new-link\" : \"\"}\r\n                           >\r\n                             Pricing\r\n                           </Link>\r\n\r\n                          ) : (\r\n                            <Link prefetch={true}\r\n                              href=\"/pricing\"\r\n                              className={url == '/pricing' ? 'new-link' : ''}>Pricing</Link>\r\n                          )}\r\n\r\n\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/blog\" className={url == '/blog' ? 'new-link' : ''}>Blog</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/status\" className={url == '/status' ? 'new-link' : ''}>Status</Link>\r\n                        </li>\r\n                        <li>\r\n                          <Link prefetch={true} href=\"/help//hc/en-us/requests/new?ticket_form_id=37293785519643\" className={url == '/help//hc/en-us/requests/new?ticket_form_id=37293785519643' ? 'new-link' : ''}>Feedback/Bugs</Link>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n              </Col>\r\n            </Row>\r\n          </Container>\r\n        </div>\r\n        <div className=\"site_footer_copyright\">\r\n          <Container>\r\n            <p>Copyright © 2025 TradeReply. All Rights Reserved.</p>\r\n          </Container>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AAGA;AACA;AATA;;;;;;;;;AAWA,MAAM,SAAS;IACb,MAAM,MAAM,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC3B,cAAc;QAGd,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;YAC7C,IAAI,MAAM,oBAAoB,GAAG;gBAC/B,cAAc;YAChB;QACF,EAAE,OAAO,GAAG;YACV,QAAQ,IAAI,CAAC;QACf;IAEF,GAAG,EAAE;IAEL,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8LAAA,CAAA,YAAS;kCACR,cAAA,8OAAC,kLAAA,CAAA,MAAG;4BAAC,WAAU;;8CACb,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,IAAI;oCAAI,IAAI;8CACtB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,UAAU;oDAAM,MAAK;8DACzB,cAAA,8OAAC;wDAAI,KAAI;wDAAkF,KAAI;;;;;;;;;;;;;;;;0DAGnG,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CASP,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,IAAI;oCAAI,IAAI;8CACtB,cAAA,8OAAC,kLAAA,CAAA,MAAG;;0DACF,8OAAC,kLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAG,IAAI;gDAAG,IAAI;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;;8EACC,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAA8B,WAAW,OAAO,gCAAgC,aAAa;kFAAI;;;;;;;;;;;8EAE9H,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAgB,WAAW,OAAO,kBAAkB,aAAa;kFAAI;;;;;;;;;;;8EAElG,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAiB,WAAW,OAAO,mBAAmB,aAAa;kFAAI;;;;;;;;;;;8EAEpG,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAIxF,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAIxF,8OAAC;8EACC,cAAA,8OAAC;wEACC,MAAK;wEACL,SAAS,CAAC;4EACR,EAAE,cAAc,IAAI,yBAAyB;4EAC7C,IAAI,OAAO,UAAU,eAAe,MAAM,EAAE,EAAE;gFAC5C,MAAM,EAAE,CAAC,UAAU,CAAC;4EACtB;wEACF;kFACD;;;;;;;;;;;8EAIH,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAS,WAAW,OAAO,WAAW,aAAa;kFAAI;;;;;;;;;;;8EAIpF,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAc,WAAW,OAAO,gBAAgB,aAAa;kFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQpG,8OAAC,kLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAG,IAAI;gDAAG,IAAI;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;;8EACC,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAkB,WAAW,OAAO,oBAAoB,aAAa;kFAAI;;;;;;;;;;;8EAEtG,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAExF,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAe,WAAW,OAAO,iBAAiB,aAAa;kFAAI;;;;;;;;;;;8EAEhG,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAY,WAAW,OAAO,cAAc,aAAa;kFAAI;;;;;;;;;;;8EAE1F,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAa,WAAW,OAAO,eAAe,aAAa;kFAAI;;;;;;;;;;;8EAE5F,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAK9F,8OAAC,kLAAA,CAAA,MAAG;gDAAC,IAAI;gDAAG,IAAI;gDAAG,IAAI;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;;8EACC,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAQ,WAAW,OAAO,gBAAgB,aAAa;kFAAI;;;;;;;;;;;8EAExF,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAW,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAExF,8OAAC;8EAQE,2BACA,8OAAC,4JAAA,CAAA,UAAI;wEACH,UAAU;wEACV,MACE,aACI,aACE,+DACA,iEACF;wEAEN,WAAW,OAAO,aAAa,aAAa;kFAC7C;;;;;iIAKA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEACd,MAAK;wEACL,WAAW,OAAO,aAAa,aAAa;kFAAI;;;;;;;;;;;8EAKtD,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAQ,WAAW,OAAO,UAAU,aAAa;kFAAI;;;;;;;;;;;8EAElF,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAAU,WAAW,OAAO,YAAY,aAAa;kFAAI;;;;;;;;;;;8EAEtF,8OAAC;8EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,UAAU;wEAAM,MAAK;wEAA6D,WAAW,OAAO,+DAA+D,aAAa;kFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU5M,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8LAAA,CAAA,YAAS;kCACR,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;uCAEe", "debugId": null}}, {"offset": {"line": 4633, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/DashboardLayout.js"], "sourcesContent": ["\"use client\";\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport SideBar from \"@/Components/common/Dashboard/SideBar\";\r\nimport Header from \"@/Components/UI/Header\";\r\nimport Footer from \"@/Components/UI/Footer\";\r\nimport \"../css/dashboard/layout.scss\";\r\nimport \"@/css/dashboard/Dashboard.scss\";\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport \"@/css/app.scss\";\r\nimport { LanguageProvider } from \"@/context/LanguageContext\";\r\nimport { useEffect, useState } from \"react\";\r\nimport Cookies from \"js-cookie\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nconst DashboardLayout = ({ children }) => {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const token = Cookies.get(\"authToken\");\r\n\r\n    if (!token) {\r\n      router.replace(\"/login\");\r\n    } else {\r\n      setIsLoading(false);\r\n    }\r\n  }, [router]);\r\n\r\n  if (isLoading) return null;\r\n\r\n  return (\r\n    <LanguageProvider>\r\n      <Header />\r\n      <main className=\"admin_layout\">\r\n        <div className=\"admin_layout_sidebar\">\r\n          <SideBar />\r\n        </div>\r\n        <div className=\"admin_layout_content\">{children}</div>\r\n      </main>\r\n      <Footer />\r\n    </LanguageProvider>\r\n\r\n  );\r\n};\r\n\r\nexport default DashboardLayout;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAKA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;;AAcA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAE1B,IAAI,CAAC,OAAO;YACV,OAAO,OAAO,CAAC;QACjB,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,WAAW,OAAO;IAEtB,qBACE,8OAAC,iIAAA,CAAA,mBAAgB;;0BACf,8OAAC,iIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oJAAA,CAAA,UAAO;;;;;;;;;;kCAEV,8OAAC;wBAAI,WAAU;kCAAwB;;;;;;;;;;;;0BAEzC,8OAAC,iIAAA,CAAA,UAAM;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 4725, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/Dashboard/AdminHeading.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport \"../../../css/dashboard/AdminHeading.scss\";\r\n\r\nconst AdminHeading = ({ heading }) => {\r\n  return (\r\n    <div\r\n      className=\"common_heading\">\r\n      <h2>{heading}</h2>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminHeading;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAIA,MAAM,eAAe,CAAC,EAAE,OAAO,EAAE;IAC/B,qBACE,8OAAC;QACC,WAAU;kBACV,cAAA,8OAAC;sBAAI;;;;;;;;;;;AAGX;uCAEe", "debugId": null}}, {"offset": {"line": 4753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/validations/errorMessages.js"], "sourcesContent": ["export const errorMessages = {\r\n  NAME_NO_LEADING_SPACES: \"Name cannot start with a space.\",\r\n  NAME_ONLY_LETTERS: \"Name can only contain letters and spaces.\",\r\n  NAME_REQUIRED: \"This field is required.\",\r\n  EMAIL_INVALID: \"Please enter a valid email address.\",\r\n  EMAIL_REQUIRED: \"This field is required.\",\r\n  PASSWORD_REQUIRED: \"This field is required.\",\r\n  PASSWORD_MIN_LENGTH: \"Must be at least 8 characters long and include letters, numbers, and symbols. Spaces are not allowed.\",\r\n  PASSWORD_UPPERCASE: \"Password must contain at least one uppercase letter. Spaces are not allowed.\",\r\n  PASSWORD_LOWERCASE: \"Password must contain at least one lowercase letter. Spaces are not allowed.\",\r\n  PASSWORD_NUMBER: \"Password must contain at least one number. Spaces are not allowed.\",\r\n  PASSWORD_SPECIAL_CHAR:\r\n    \"Password must contain at least one special character. Spaces are not allowed.\",\r\n  PASSWORD_CONFIRMATION: \"Passwords do not match. Please try again.\",\r\n  PASSWORD_CONFIRMATION_REQUIRED: \"This field is required.\"\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,gBAAgB;IAC3B,wBAAwB;IACxB,mBAAmB;IACnB,eAAe;IACf,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,qBAAqB;IACrB,oBAAoB;IACpB,oBAAoB;IACpB,iBAAiB;IACjB,uBACE;IACF,uBAAuB;IACvB,gCAAgC;AAClC", "debugId": null}}, {"offset": {"line": 4775, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/validations/schema.js"], "sourcesContent": ["import * as Yup from \"yup\";\r\nimport { errorMessages } from \"./errorMessages\";\r\n\r\nexport const loginSchema = Yup.object({\r\n  email: Yup.string()\r\n    .email(errorMessages.EMAIL_INVALID)\r\n    .matches(/\\.[a-zA-Z]{2,4}$/, errorMessages.EMAIL_INVALID)\r\n    .required(errorMessages.EMAIL_REQUIRED),\r\n  password: Yup.string()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n  // .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n  // .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n  // .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n  // .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n  // .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n});\r\n\r\nexport const signupSchema = Yup.object({\r\n  email: Yup.string()\r\n    .trim()\r\n    .email(errorMessages.EMAIL_INVALID)\r\n    .required(errorMessages.EMAIL_REQUIRED)\r\n    .matches(/\\.[a-zA-Z]{2,4}$/, errorMessages.EMAIL_INVALID),\r\n  password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n    .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n  password_confirmation: Yup.string()\r\n    .trim()\r\n    .oneOf([Yup.ref('password'), null], errorMessages.PASSWORD_CONFIRMATION)\r\n    .required(errorMessages.PASSWORD_CONFIRMATION_REQUIRED),\r\n});\r\n\r\nexport const changePasswordSchema = Yup.object({\r\n  current_password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED),\r\n  new_password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n    .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n  confirm_new_password: Yup.string()\r\n    .trim()\r\n    .oneOf([Yup.ref('new_password'), null], errorMessages.PASSWORD_CONFIRMATION)\r\n    .required(errorMessages.PASSWORD_CONFIRMATION_REQUIRED),\r\n});\r\n\r\nexport const localAccountSchema = Yup.object({\r\n  emailOrUsername: Yup.string()\r\n    .test(\"is-email-or-username\", \"Enter a valid email or username\", (value) => {\r\n      if (!value) return false; // Reject empty input\r\n\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/; // Standard email pattern\r\n      const usernameRegex = /^[a-zA-Z0-9_.]+$/; // Allows letters, numbers, underscores, and dots\r\n\r\n      return emailRegex.test(value) || usernameRegex.test(value);\r\n    })\r\n    .required(\"Email or username is required\"),\r\n});\r\n\r\n\r\nexport const forgetSchema = Yup.object({\r\n  email: Yup.string()\r\n    .email(errorMessages.EMAIL_INVALID)\r\n    .required(errorMessages.EMAIL_REQUIRED),\r\n});\r\n\r\n\r\nexport const createUsernameSchema = Yup.object().shape({\r\n  name: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"User name cannot exceed 100 characters\"),\r\n  // .test(\"unique\", \"This username is already taken. Please choose another one.\"),\r\n});\r\nexport const securitySchema = Yup.object().shape({\r\n  security_code: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n});\r\nexport const resetSchema = Yup.object({\r\n  password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n    .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n  password_confirmation: Yup.string()\r\n    .oneOf([Yup.ref(\"password\")], \"Passwords must match\")\r\n    .required(\"Password confirmation is required\"),\r\n});\r\n\r\nexport const blogValidationSchema = Yup.object().shape({\r\n  title: Yup.string()\r\n    .trim()\r\n    .required(\"Title is required\")\r\n    .min(5, \"Title must be at least 5 characters\")\r\n    .max(100, \"Title cannot exceed 100 characters\"),\r\n  content: Yup.string()\r\n    .trim()\r\n    .required(\"Content is required\")\r\n    .min(50, \"Content must be at least 20 characters\")\r\n    .max(1000, \"Content cannot exceed 100 characters\"),\r\n});\r\n\r\nexport const titleDescValidationSchema = Yup.object().shape({\r\n  title: Yup.string()\r\n    .trim()\r\n    .required(\"Title is required\")\r\n    .min(5, \"Title must be at least 5 characters\")\r\n    .max(100, \"Title cannot exceed 100 characters\"),\r\n  content: Yup.string()\r\n    .trim()\r\n    .required(\"Content is required\")\r\n    .min(50, \"Content must be at least 20 characters\")\r\n    .max(1000, \"Content cannot exceed 100 characters\"),\r\n});\r\n\r\nexport const ArticleSchema = Yup.object({\r\n  primary_category_id: Yup.string().required(\"Primary Category is required\"),\r\n  // secondary_category_id: Yup.array()\r\n  //   .of(Yup.string())\r\n  //   .min(1, \"Select at least one secondary category\")\r\n  //   .required(\"Secondary Category is required\"),\r\n  title: Yup.string().required(\"Page Title is required\"),\r\n  title: Yup.string().required(\"Image URL is required\"),\r\n\r\n  content: Yup.string().required(\"Body Text is required\"),\r\n  summary: Yup.string()\r\n    .max(250, \"Max 250 characters allowed\")\r\n    .required(\"Page Summary is required\"),\r\n});\r\nexport const checkoutSchema = Yup.object({\r\n  firstName: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"First name cannot exceed 100 characters\"),\r\n  lastName: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"Last name cannot exceed 100 characters\"),\r\n  country: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\"),\r\n  address: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"Address cannot exceed 100 characters\"),\r\n  // cardNumber: Yup.string()\r\n  //   .matches(/^\\d{4} \\d{4} \\d{4} \\d{4}$/, \"Card number must be in format 1234 5678 9012 3456\")\r\n  //   .required(\"Card number is required\"),\r\n  // expireDate: Yup.string()\r\n  //   .matches(/^(0[1-9]|1[0-2]) \\/ \\d{4}$/, \"Date must be in MM / YYYY format\")\r\n  //   .required(\"This field is required\"),\r\n\r\n  // securityCode: Yup.string()\r\n  //   .matches(/^\\d{3}$/, \"Code must be 3 digits\")\r\n  //   .required(\"This field is required\"),\r\n});"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,MAAM,cAAc,mIAAA,CAAA,SAAU,CAAC;IACpC,OAAO,mIAAA,CAAA,SAAU,GACd,KAAK,CAAC,mIAAA,CAAA,gBAAa,CAAC,aAAa,EACjC,OAAO,CAAC,oBAAoB,mIAAA,CAAA,gBAAa,CAAC,aAAa,EACvD,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,cAAc;IACxC,UAAU,mIAAA,CAAA,SAAU,GACjB,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,iBAAiB;AAM7C;AAEO,MAAM,eAAe,mIAAA,CAAA,SAAU,CAAC;IACrC,OAAO,mIAAA,CAAA,SAAU,GACd,IAAI,GACJ,KAAK,CAAC,mIAAA,CAAA,gBAAa,CAAC,aAAa,EACjC,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,cAAc,EACrC,OAAO,CAAC,oBAAoB,mIAAA,CAAA,gBAAa,CAAC,aAAa;IAC1D,UAAU,mIAAA,CAAA,SAAU,GACjB,IAAI,GACJ,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,iBAAiB,EACxC,GAAG,CAAC,GAAG,mIAAA,CAAA,gBAAa,CAAC,mBAAmB,EACxC,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,eAAe,EAC9C,OAAO,CAAC,cAAc,mIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5D,uBAAuB,mIAAA,CAAA,SAAU,GAC9B,IAAI,GACJ,KAAK,CAAC;QAAC,mIAAA,CAAA,MAAO,CAAC;QAAa;KAAK,EAAE,mIAAA,CAAA,gBAAa,CAAC,qBAAqB,EACtE,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,8BAA8B;AAC1D;AAEO,MAAM,uBAAuB,mIAAA,CAAA,SAAU,CAAC;IAC7C,kBAAkB,mIAAA,CAAA,SAAU,GACzB,IAAI,GACJ,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,iBAAiB;IAC3C,cAAc,mIAAA,CAAA,SAAU,GACrB,IAAI,GACJ,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,iBAAiB,EACxC,GAAG,CAAC,GAAG,mIAAA,CAAA,gBAAa,CAAC,mBAAmB,EACxC,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,eAAe,EAC9C,OAAO,CAAC,cAAc,mIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5D,sBAAsB,mIAAA,CAAA,SAAU,GAC7B,IAAI,GACJ,KAAK,CAAC;QAAC,mIAAA,CAAA,MAAO,CAAC;QAAiB;KAAK,EAAE,mIAAA,CAAA,gBAAa,CAAC,qBAAqB,EAC1E,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,8BAA8B;AAC1D;AAEO,MAAM,qBAAqB,mIAAA,CAAA,SAAU,CAAC;IAC3C,iBAAiB,mIAAA,CAAA,SAAU,GACxB,IAAI,CAAC,wBAAwB,mCAAmC,CAAC;QAChE,IAAI,CAAC,OAAO,OAAO,OAAO,qBAAqB;QAE/C,MAAM,aAAa,8BAA8B,yBAAyB;QAC1E,MAAM,gBAAgB,oBAAoB,iDAAiD;QAE3F,OAAO,WAAW,IAAI,CAAC,UAAU,cAAc,IAAI,CAAC;IACtD,GACC,QAAQ,CAAC;AACd;AAGO,MAAM,eAAe,mIAAA,CAAA,SAAU,CAAC;IACrC,OAAO,mIAAA,CAAA,SAAU,GACd,KAAK,CAAC,mIAAA,CAAA,gBAAa,CAAC,aAAa,EACjC,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,cAAc;AAC1C;AAGO,MAAM,uBAAuB,mIAAA,CAAA,SAAU,GAAG,KAAK,CAAC;IACrD,MAAM,mIAAA,CAAA,SAAU,GACb,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;AAEd;AACO,MAAM,iBAAiB,mIAAA,CAAA,SAAU,GAAG,KAAK,CAAC;IAC/C,eAAe,mIAAA,CAAA,SAAU,GACtB,IAAI,GACJ,QAAQ,CAAC;AACd;AACO,MAAM,cAAc,mIAAA,CAAA,SAAU,CAAC;IACpC,UAAU,mIAAA,CAAA,SAAU,GACjB,IAAI,GACJ,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,iBAAiB,EACxC,GAAG,CAAC,GAAG,mIAAA,CAAA,gBAAa,CAAC,mBAAmB,EACxC,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,eAAe,EAC9C,OAAO,CAAC,cAAc,mIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5D,uBAAuB,mIAAA,CAAA,SAAU,GAC9B,KAAK,CAAC;QAAC,mIAAA,CAAA,MAAO,CAAC;KAAY,EAAE,wBAC7B,QAAQ,CAAC;AACd;AAEO,MAAM,uBAAuB,mIAAA,CAAA,SAAU,GAAG,KAAK,CAAC;IACrD,OAAO,mIAAA,CAAA,SAAU,GACd,IAAI,GACJ,QAAQ,CAAC,qBACT,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK;IACZ,SAAS,mIAAA,CAAA,SAAU,GAChB,IAAI,GACJ,QAAQ,CAAC,uBACT,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,MAAM;AACf;AAEO,MAAM,4BAA4B,mIAAA,CAAA,SAAU,GAAG,KAAK,CAAC;IAC1D,OAAO,mIAAA,CAAA,SAAU,GACd,IAAI,GACJ,QAAQ,CAAC,qBACT,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK;IACZ,SAAS,mIAAA,CAAA,SAAU,GAChB,IAAI,GACJ,QAAQ,CAAC,uBACT,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,MAAM;AACf;AAEO,MAAM,gBAAgB,mIAAA,CAAA,SAAU,CAAC;IACtC,qBAAqB,mIAAA,CAAA,SAAU,GAAG,QAAQ,CAAC;IAC3C,qCAAqC;IACrC,sBAAsB;IACtB,sDAAsD;IACtD,iDAAiD;IACjD,OAAO,mIAAA,CAAA,SAAU,GAAG,QAAQ,CAAC;IAC7B,OAAO,mIAAA,CAAA,SAAU,GAAG,QAAQ,CAAC;IAE7B,SAAS,mIAAA,CAAA,SAAU,GAAG,QAAQ,CAAC;IAC/B,SAAS,mIAAA,CAAA,SAAU,GAChB,GAAG,CAAC,KAAK,8BACT,QAAQ,CAAC;AACd;AACO,MAAM,iBAAiB,mIAAA,CAAA,SAAU,CAAC;IACvC,WAAW,mIAAA,CAAA,SAAU,GAClB,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;IACZ,UAAU,mIAAA,CAAA,SAAU,GACjB,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;IACZ,SAAS,mIAAA,CAAA,SAAU,GAChB,IAAI,GACJ,QAAQ,CAAC;IACZ,SAAS,mIAAA,CAAA,SAAU,GAChB,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;AAWd", "debugId": null}}, {"offset": {"line": 4865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/JoditEditorComponent.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useRef, useEffect, useState } from \"react\";\r\nimport dynamic from \"next/dynamic\";\r\nconst JoditEditor = dynamic(() => import(\"jodit-react\"), { ssr: false });\r\n\r\nconst JoditEditorComponent = ({ value, onChange }) => {\r\n  const editor = useRef(null);\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsClient(true); \r\n  }, []);\r\n\r\n  const config = {\r\n    readonly: false,\r\n    height: 400,\r\n    toolbarSticky: true,\r\n    toolbarAdaptive: false,\r\n  };\r\n\r\n  return isClient ? (\r\n    <JoditEditor\r\n      ref={editor}\r\n      value={value} \r\n      config={config}\r\n      onBlur={(newContent) => onChange(newContent)} \r\n    />\r\n  ) : (\r\n    <p>Loading Editor...</p> \r\n  );\r\n};\r\n\r\nexport default JoditEditorComponent;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;AAHA;;;;AAIA,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAAiC,KAAK;;AAEhE,MAAM,uBAAuB,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,SAAS;QACb,UAAU;QACV,QAAQ;QACR,eAAe;QACf,iBAAiB;IACnB;IAEA,OAAO,yBACL,8OAAC;QACC,KAAK;QACL,OAAO;QACP,QAAQ;QACR,QAAQ,CAAC,aAAe,SAAS;;;;;iEAGnC,8OAAC;kBAAE;;;;;;AAEP;uCAEe", "debugId": null}}, {"offset": {"line": 4918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/ListingTable.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { FaEdit, FaTrash } from \"react-icons/fa\";\r\n\r\nconst ListingTable = ({ \r\n  array, \r\n  onUpdate, \r\n  onDelete, \r\n  categoryFlag = false,\r\n  onSort,\r\n  sortField,\r\n  sortOrder \r\n}) => {\r\n  const getSortIcon = (field) => {\r\n    if (sortField !== field) return \"⬍\";\r\n    return sortOrder === \"asc\" ? \"↑\" : \"↓\";\r\n  };\r\n\r\n  const limitText = (text, limit = 80) => {\r\n    if (!text) return \"\";\r\n    if (text.length <= limit) return text;\r\n\r\n    const trimmedText = text.substring(0, limit);\r\n    const lastSpaceIndex = trimmedText.lastIndexOf(\" \");\r\n\r\n    return lastSpaceIndex > 0\r\n      ? trimmedText.substring(0, lastSpaceIndex) + \"...\"\r\n      : trimmedText + \"...\";\r\n  };\r\n\r\n  return (\r\n    <table className=\"table\">\r\n      <thead>\r\n        <tr>\r\n          <th>\r\n            <input type=\"checkbox\" />\r\n          </th>\r\n          {!categoryFlag && array[0]?.primary_category && (\r\n            <th onClick={() => onSort && onSort(\"primary_category.title\")}>\r\n              Category {onSort && getSortIcon(\"primary_category.title\")}\r\n            </th>\r\n          )}\r\n          <th onClick={() => onSort && onSort(\"title\")}>\r\n            Name {onSort && getSortIcon(\"title\")}\r\n          </th>\r\n          <th onClick={() => onSort && onSort(\"summary\")}>\r\n            Description {onSort && getSortIcon(\"summary\")}\r\n          </th>\r\n          {array[0]?.count != null && (\r\n            <th onClick={() => onSort && onSort(\"count\")}>\r\n              Count {onSort && getSortIcon(\"count\")}\r\n            </th>\r\n          )}\r\n          <th>Action</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        {array.map((item, index) => (\r\n          <tr key={index}>\r\n            <td>\r\n              <input type=\"checkbox\" />\r\n            </td>\r\n            {!categoryFlag && (\r\n              <td>\r\n                {item?.primary_category ? item.primary_category.title : \"N/A\"}\r\n              </td>\r\n            )}\r\n            <td>{item?.title}</td>\r\n            <td>\r\n              <span>\r\n                {item?.count != null\r\n                  ? limitText(item?.content)\r\n                  : limitText(item?.summary)}\r\n              </span>\r\n            </td>\r\n            {item?.count != null && <td>{item.count}</td>}\r\n            <td>\r\n              <button\r\n                onClick={() => onUpdate(item)}\r\n                style={{ marginRight: \"10px\", cursor: \"pointer\" }}\r\n              >\r\n                <FaEdit color=\"blue\" size={16} />\r\n              </button>\r\n              <button\r\n                onClick={() => onDelete(item?.id, item?.slug)}\r\n                style={{ cursor: \"pointer\" }}\r\n              >\r\n                <FaTrash color=\"red\" size={16} />\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        ))}\r\n      </tbody>\r\n    </table>\r\n  );\r\n};\r\n\r\nexport default ListingTable;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,eAAe,CAAC,EACpB,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,eAAe,KAAK,EACpB,MAAM,EACN,SAAS,EACT,SAAS,EACV;IACC,MAAM,cAAc,CAAC;QACnB,IAAI,cAAc,OAAO,OAAO;QAChC,OAAO,cAAc,QAAQ,MAAM;IACrC;IAEA,MAAM,YAAY,CAAC,MAAM,QAAQ,EAAE;QACjC,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,KAAK,MAAM,IAAI,OAAO,OAAO;QAEjC,MAAM,cAAc,KAAK,SAAS,CAAC,GAAG;QACtC,MAAM,iBAAiB,YAAY,WAAW,CAAC;QAE/C,OAAO,iBAAiB,IACpB,YAAY,SAAS,CAAC,GAAG,kBAAkB,QAC3C,cAAc;IACpB;IAEA,qBACE,8OAAC;QAAM,WAAU;;0BACf,8OAAC;0BACC,cAAA,8OAAC;;sCACC,8OAAC;sCACC,cAAA,8OAAC;gCAAM,MAAK;;;;;;;;;;;wBAEb,CAAC,gBAAgB,KAAK,CAAC,EAAE,EAAE,kCAC1B,8OAAC;4BAAG,SAAS,IAAM,UAAU,OAAO;;gCAA2B;gCACnD,UAAU,YAAY;;;;;;;sCAGpC,8OAAC;4BAAG,SAAS,IAAM,UAAU,OAAO;;gCAAU;gCACtC,UAAU,YAAY;;;;;;;sCAE9B,8OAAC;4BAAG,SAAS,IAAM,UAAU,OAAO;;gCAAY;gCACjC,UAAU,YAAY;;;;;;;wBAEpC,KAAK,CAAC,EAAE,EAAE,SAAS,sBAClB,8OAAC;4BAAG,SAAS,IAAM,UAAU,OAAO;;gCAAU;gCACrC,UAAU,YAAY;;;;;;;sCAGjC,8OAAC;sCAAG;;;;;;;;;;;;;;;;;0BAGR,8OAAC;0BACE,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;;0CACC,8OAAC;0CACC,cAAA,8OAAC;oCAAM,MAAK;;;;;;;;;;;4BAEb,CAAC,8BACA,8OAAC;0CACE,MAAM,mBAAmB,KAAK,gBAAgB,CAAC,KAAK,GAAG;;;;;;0CAG5D,8OAAC;0CAAI,MAAM;;;;;;0CACX,8OAAC;0CACC,cAAA,8OAAC;8CACE,MAAM,SAAS,OACZ,UAAU,MAAM,WAChB,UAAU,MAAM;;;;;;;;;;;4BAGvB,MAAM,SAAS,sBAAQ,8OAAC;0CAAI,KAAK,KAAK;;;;;;0CACvC,8OAAC;;kDACC,8OAAC;wCACC,SAAS,IAAM,SAAS;wCACxB,OAAO;4CAAE,aAAa;4CAAQ,QAAQ;wCAAU;kDAEhD,cAAA,8OAAC,8IAAA,CAAA,SAAM;4CAAC,OAAM;4CAAO,MAAM;;;;;;;;;;;kDAE7B,8OAAC;wCACC,SAAS,IAAM,SAAS,MAAM,IAAI,MAAM;wCACxC,OAAO;4CAAE,QAAQ;wCAAU;kDAE3B,cAAA,8OAAC,8IAAA,CAAA,UAAO;4CAAC,OAAM;4CAAM,MAAM;;;;;;;;;;;;;;;;;;uBA7BxB;;;;;;;;;;;;;;;;AAqCnB;uCAEe", "debugId": null}}, {"offset": {"line": 5138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/CustomPagination.js"], "sourcesContent": ["'use client';\r\n\r\nimport { Pagination } from \"react-bootstrap\";\r\nimport Link from \"next/link\";\r\nimport { RightArrowIcon } from '@/assets/svgIcons/SvgIcon';\r\nimport \"@/css/common/CustomPagination.scss\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { useSearchParams } from \"next/navigation\";\r\n\r\n\r\n\r\nexport default function CustomPagination({ links, onDataSend, useLinks = true, pageUrl = \"blog\", useDynamicParams = false }) {\r\n  if (!links || links.total === 0) return null;\r\n  const searchParams = useSearchParams()\r\n  const pathname = usePathname();\r\n  const totalPages = links.total;\r\n  const currentPage = links.current_page;\r\n\r\n  // Store correct total pages in cookies\r\n  const isSearch = !!searchParams.get('key');\r\n  const startPage = Math.max(1, Math.min(currentPage - 1, totalPages - 2));\r\n  const visiblePages = Array.from({ length: Math.min(3, totalPages) }, (_, i) => startPage + i);\r\n  const pathParts = pathname?.split('/').filter(Boolean); // Remove empty segments\r\n  const PageLinkUrl = pathParts[0];\r\n\r\n  const getLink = (page) => {\r\n    if (useDynamicParams) {\r\n      return page === 1 ? `/${pageUrl}` : `/${pageUrl}/page/${page}`;\r\n    } else {\r\n      const params = new URLSearchParams(searchParams.toString());\r\n      params.set('page', page);\r\n      if(PageLinkUrl === 'super-admin') {\r\n        return `/${PageLinkUrl}/${pathParts[1]}?${params.toString()}` \r\n      }\r\n      return `/${PageLinkUrl}?${params.toString()}`;\r\n    }\r\n  };\r\n\r\n  const renderPageItem = (page) => {\r\n    return useLinks ? (\r\n      <Link\r\n        href={getLink(page)}\r\n        className={`mx-2 ${page === currentPage ? 'active' : ''}`}\r\n        key={page}\r\n        onClick={(e) => {\r\n        // e.preventDefault();\r\n        onDataSend(page);\r\n      }}\r\n      >\r\n        {page}\r\n      </Link>\r\n    ) : (\r\n      <Link\r\n        className={`mx-2 link ${page === currentPage ? 'active' : ''}`}\r\n        key={page}\r\n        href={getLink(page)}\r\n        onClick={(e) => {\r\n          // e.preventDefault();\r\n          onDataSend(page);\r\n        }}\r\n\r\n      >\r\n        {page}\r\n      </Link>\r\n    );\r\n  };\r\n\r\n\r\n  return (\r\n    <div className=\"customPagination\">\r\n      <Pagination>\r\n        {/* Previous Page */}\r\n        {links.prev_page ? (\r\n          <Pagination.Prev\r\n            as={Link}\r\n\r\n            href={getLink(currentPage - 1)}\r\n            className=\"prevArrow\"\r\n            rel=\"prev\"\r\n            aria-label=\"Previous Page\"\r\n          onClick={() => {\r\n\r\n            // e.preventDefault();\r\n            onDataSend(currentPage - 1)\r\n          }}\r\n\r\n          >\r\n            <RightArrowIcon />\r\n          </Pagination.Prev>\r\n        ) : (\r\n          <Pagination.Prev disabled className=\"prevArrow\">\r\n            <RightArrowIcon />\r\n          </Pagination.Prev>\r\n        )}\r\n\r\n        {/* First Page */}\r\n        {!visiblePages.includes(1) && (\r\n          <>\r\n            {renderPageItem(1)}\r\n            <span className=\"txt-blue\">...</span>\r\n          </>\r\n        )}\r\n\r\n        {/* Page Numbers */}\r\n        {visiblePages.map(renderPageItem)}\r\n\r\n        {/* Last Page */}\r\n        {!visiblePages.includes(totalPages) && (\r\n          <>\r\n            <span className=\"txt-blue\">...</span>\r\n            {renderPageItem(totalPages)}\r\n          </>\r\n        )}\r\n\r\n        {/* Next Page */}\r\n        {links.next_page ? (\r\n          <Pagination.Next\r\n            as={Link}\r\n            href={getLink(currentPage + 1)}\r\n            className=\"nextArrow\"\r\n            rel=\"next\"\r\n            aria-label=\"Next Page\"\r\n          onClick={(e) => {\r\n            // e.preventDefault();\r\n            onDataSend(currentPage + 1);\r\n          }}\r\n          >\r\n            <RightArrowIcon />\r\n          </Pagination.Next>\r\n        ) : (\r\n          <Pagination.Next disabled className=\"nextArrow\">\r\n            <RightArrowIcon />\r\n          </Pagination.Next>\r\n        )}\r\n      </Pagination>\r\n    </div>\r\n  );\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;;;AAWe,SAAS,iBAAiB,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,IAAI,EAAE,UAAU,MAAM,EAAE,mBAAmB,KAAK,EAAE;IACzH,IAAI,CAAC,SAAS,MAAM,KAAK,KAAK,GAAG,OAAO;IACxC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,aAAa,MAAM,KAAK;IAC9B,MAAM,cAAc,MAAM,YAAY;IAEtC,uCAAuC;IACvC,MAAM,WAAW,CAAC,CAAC,aAAa,GAAG,CAAC;IACpC,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,cAAc,GAAG,aAAa;IACrE,MAAM,eAAe,MAAM,IAAI,CAAC;QAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;IAAY,GAAG,CAAC,GAAG,IAAM,YAAY;IAC3F,MAAM,YAAY,UAAU,MAAM,KAAK,OAAO,UAAU,wBAAwB;IAChF,MAAM,cAAc,SAAS,CAAC,EAAE;IAEhC,MAAM,UAAU,CAAC;QACf,IAAI,kBAAkB;YACpB,OAAO,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,EAAE,QAAQ,MAAM,EAAE,MAAM;QAChE,OAAO;YACL,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;YACxD,OAAO,GAAG,CAAC,QAAQ;YACnB,IAAG,gBAAgB,eAAe;gBAChC,OAAO,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;YAC/D;YACA,OAAO,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,OAAO,QAAQ,IAAI;QAC/C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,yBACL,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM,QAAQ;YACd,WAAW,CAAC,KAAK,EAAE,SAAS,cAAc,WAAW,IAAI;YAEzD,SAAS,CAAC;gBACV,sBAAsB;gBACtB,WAAW;YACb;sBAEG;WANI;;;;iCASP,8OAAC,4JAAA,CAAA,UAAI;YACH,WAAW,CAAC,UAAU,EAAE,SAAS,cAAc,WAAW,IAAI;YAE9D,MAAM,QAAQ;YACd,SAAS,CAAC;gBACR,sBAAsB;gBACtB,WAAW;YACb;sBAGC;WARI;;;;;IAWX;IAGA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gMAAA,CAAA,aAAU;;gBAER,MAAM,SAAS,iBACd,8OAAC,gMAAA,CAAA,aAAU,CAAC,IAAI;oBACd,IAAI,4JAAA,CAAA,UAAI;oBAER,MAAM,QAAQ,cAAc;oBAC5B,WAAU;oBACV,KAAI;oBACJ,cAAW;oBACb,SAAS;wBAEP,sBAAsB;wBACtB,WAAW,cAAc;oBAC3B;8BAGE,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;yCAGjB,8OAAC,gMAAA,CAAA,aAAU,CAAC,IAAI;oBAAC,QAAQ;oBAAC,WAAU;8BAClC,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;gBAKlB,CAAC,aAAa,QAAQ,CAAC,oBACtB;;wBACG,eAAe;sCAChB,8OAAC;4BAAK,WAAU;sCAAW;;;;;;;;gBAK9B,aAAa,GAAG,CAAC;gBAGjB,CAAC,aAAa,QAAQ,CAAC,6BACtB;;sCACE,8OAAC;4BAAK,WAAU;sCAAW;;;;;;wBAC1B,eAAe;;;gBAKnB,MAAM,SAAS,iBACd,8OAAC,gMAAA,CAAA,aAAU,CAAC,IAAI;oBACd,IAAI,4JAAA,CAAA,UAAI;oBACR,MAAM,QAAQ,cAAc;oBAC5B,WAAU;oBACV,KAAI;oBACJ,cAAW;oBACb,SAAS,CAAC;wBACR,sBAAsB;wBACtB,WAAW,cAAc;oBAC3B;8BAEE,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;yCAGjB,8OAAC,gMAAA,CAAA,aAAU,CAAC,IAAI;oBAAC,QAAQ;oBAAC,WAAU;8BAClC,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 5318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/super-admin/education/page.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Container, Row, Col, Form, Button } from \"react-bootstrap\";\r\nimport { useFormik } from \"formik\";\r\nimport DashboardLayout from \"@/Layouts/DashboardLayout\";\r\nimport AdminHeading from \"@/Components/common/Dashboard/AdminHeading\";\r\nimport toast from \"react-hot-toast\";\r\nimport { deleteRequest, get, post, put } from \"@/utils/apiUtils\";\r\nimport { ArticleSchema } from \"@/validations/schema\";\r\nimport JoditEditorComponent from \"@/Components/UI/JoditEditorComponent\";\r\nimport ListingTable from \"@/Components/UI/ListingTable\";\r\nimport \"@/css/dashboard/StrategyManager.scss\";\r\nimport dynamic from \"next/dynamic\";\r\nimport CustomPagination from \"@/Components/UI/CustomPagination\";\r\nconst Select = dynamic(() => import(\"react-select\"), { ssr: false });\r\n\r\nconst EducationForm = () => {\r\n  const [meta, setMeta] = useState(null);\r\n  const [newPage, setNewPage] = useState(1);\r\n  const [fetchCategories, setFetchCategories] = useState([]);\r\n  const [listingEducationArticles, setListingEducationArticles] = useState([]);\r\n  const [allEducationArticles, setAllEducationArticles] = useState([]);\r\n  const [editingEducation, setEditingEducation] = useState(null);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [isLoadingAllData, setIsLoadingAllData] = useState(false);\r\n  const [filteredAndSortedArticles, setFilteredAndSortedArticles] = useState([]);\r\n  // Initialize sort state with title field and ascending order\r\n  const [sortField, setSortField] = useState(\"title\");\r\n  const [sortOrder, setSortOrder] = useState(\"asc\");\r\n\r\n  // Pagination settings\r\n  const ITEMS_PER_PAGE = 10;\r\n\r\n  useEffect(() => {\r\n    const listingCategories = async () => {\r\n      try {\r\n        const response = await get(\"/super-admin/articles/create\");\r\n        setFetchCategories(Array.isArray(response?.data) ? response.data : []);\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    };\r\n\r\n    listingCategories();\r\n  }, []);\r\n\r\n\r\n  // Function to fetch ALL education articles from all pages\r\n  const fetchAllEducationArticles = async () => {\r\n    setIsLoadingAllData(true);\r\n    try {\r\n      let allArticles = [];\r\n      let currentPage = 1;\r\n      let hasMorePages = true;\r\n\r\n      while (hasMorePages) {\r\n        const response = await get(\"super-admin/articles/list/education\", { page: currentPage });\r\n        console.log(response);\r\n        const articles = response?.data || [];\r\n        const pagination = response?.pagination || {};\r\n\r\n        allArticles = [...allArticles, ...articles];\r\n\r\n        // Check if there are more pages\r\n        hasMorePages = pagination.next_page_url !== null;\r\n        currentPage++;\r\n      }\r\n\r\n      // Sort articles alphabetically by title before setting them\r\n      const sortedArticles = allArticles.sort((a, b) => \r\n        (a.title || '').localeCompare(b.title || '')\r\n      );\r\n\r\n      setAllEducationArticles(sortedArticles);\r\n      return sortedArticles;\r\n    } catch (error) {\r\n      console.error(\"Error fetching all education articles:\", error);\r\n      return [];\r\n    } finally {\r\n      setIsLoadingAllData(false);\r\n    }\r\n  };\r\n\r\n  // Helper function to get nested property value\r\n  const getNestedValue = (obj, path) => {\r\n    return path.split('.').reduce((current, key) => current?.[key], obj) || '';\r\n  };\r\n\r\n  // Function to apply search and sorting to all articles\r\n  const applySearchAndSort = (articles, searchTerm, field = \"title\", order = \"asc\") => {\r\n    let filtered = [...articles];\r\n\r\n    // Apply search filter - search ONLY in title field with prioritization\r\n    if (searchTerm.trim()) {\r\n      const lowerSearchTerm = searchTerm.toLowerCase();\r\n\r\n      // Separate articles into two groups: those starting with search term and those containing it elsewhere\r\n      const startsWithTerm = [];\r\n      const containsTerm = [];\r\n\r\n      articles.forEach(article => {\r\n        const title = article.title?.toLowerCase() || '';\r\n        if (title.startsWith(lowerSearchTerm)) {\r\n          startsWithTerm.push(article);\r\n        } else if (title.includes(lowerSearchTerm)) {\r\n          containsTerm.push(article);\r\n        }\r\n      });\r\n\r\n      // Sort each group separately to maintain priority\r\n      const sortFunction = (a, b) => {\r\n        const aValue = getNestedValue(a, field);\r\n        const bValue = getNestedValue(b, field);\r\n        return order === \"asc\"\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      };\r\n\r\n      startsWithTerm.sort(sortFunction);\r\n      containsTerm.sort(sortFunction);\r\n\r\n      // Combine results with priority: starts with term first, then contains term\r\n      filtered = [...startsWithTerm, ...containsTerm];\r\n    } else {\r\n      // Apply normal sorting when no search term\r\n      filtered.sort((a, b) => {\r\n        const aValue = getNestedValue(a, field);\r\n        const bValue = getNestedValue(b, field);\r\n        return order === \"asc\"\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      });\r\n    }\r\n\r\n    return filtered;\r\n  };\r\n\r\n  // Function to paginate the filtered and sorted results\r\n  const paginateResults = (articles, page, itemsPerPage) => {\r\n    const startIndex = (page - 1) * itemsPerPage;\r\n    const endIndex = startIndex + itemsPerPage;\r\n    const paginatedArticles = articles.slice(startIndex, endIndex);\r\n\r\n    const totalPages = Math.ceil(articles.length / itemsPerPage);\r\n\r\n    return {\r\n      data: paginatedArticles,\r\n      pagination: {\r\n        current_page: page,\r\n        per_page: itemsPerPage,\r\n        total: totalPages,\r\n        count: paginatedArticles.length,\r\n        next_page_url: page < totalPages ? `page=${page + 1}` : null,\r\n        prev_page_url: page > 1 ? `page=${page - 1}` : null,\r\n        total_records: articles.length\r\n      }\r\n    };\r\n  };\r\n\r\n  // Load all data on component mount\r\n  useEffect(() => {\r\n    fetchAllEducationArticles();\r\n  }, []);\r\n\r\n  // Apply search and sorting whenever allEducationArticles, searchTerm, or sort parameters change\r\n  useEffect(() => {\r\n    if (allEducationArticles.length > 0) {\r\n      const filtered = applySearchAndSort(allEducationArticles, searchTerm, sortField, sortOrder);\r\n      setFilteredAndSortedArticles(filtered);\r\n\r\n      // Reset to page 1 when search term or sort changes\r\n      if (searchTerm !== '' || sortField) {\r\n        setNewPage(1);\r\n      }\r\n    }\r\n  }, [allEducationArticles, searchTerm, sortField, sortOrder]);\r\n\r\n  // Update displayed articles and pagination when page or filtered results change\r\n  useEffect(() => {\r\n    if (filteredAndSortedArticles.length > 0) {\r\n      const result = paginateResults(filteredAndSortedArticles, newPage, ITEMS_PER_PAGE);\r\n      setListingEducationArticles(result.data);\r\n      setMeta(result.pagination);\r\n    } else if (filteredAndSortedArticles.length === 0 && allEducationArticles.length > 0) {\r\n      // No search results\r\n      setListingEducationArticles([]);\r\n      setMeta({\r\n        current_page: 1,\r\n        per_page: ITEMS_PER_PAGE,\r\n        total: 0,\r\n        count: 0,\r\n        next_page_url: null,\r\n        prev_page_url: null,\r\n        total_records: 0\r\n      });\r\n    }\r\n  }, [filteredAndSortedArticles, newPage]);\r\n\r\n  const handleSearch = (event) => {\r\n    setSearchTerm(event.target.value);\r\n  };\r\n\r\n  const handlePrimaryCategoryChange = (event) => {\r\n    const selectedPrimaryCategory = event.target.value;\r\n    formik.setFieldValue(\"primary_category_id\", selectedPrimaryCategory);\r\n\r\n    if (selectedPrimaryCategory) {\r\n      const updatedSecondaryCategories = formik.values.secondary_categories.filter(\r\n        (id) => String(id) !== String(selectedPrimaryCategory)\r\n      );\r\n      formik.setFieldValue(\"secondary_categories\", updatedSecondaryCategories);\r\n    }\r\n  };\r\n\r\n  const getFilteredSecondaryOptions = () => {\r\n    return (fetchCategories || [])\r\n      .filter((cat) => String(cat.id) !== String(formik.values.primary_category_id))\r\n      .map((cat) => ({\r\n        value: cat.id,\r\n        label: cat.title\r\n      }));\r\n  };\r\n\r\n  const getSelectedSecondaryValues = () => {\r\n    return formik.values.secondary_categories\r\n      .map((categoryId) => {\r\n        const category = fetchCategories.find((cat) => String(cat.id) === String(categoryId));\r\n        if (!category) return null;\r\n        return { value: category.id, label: category.title };\r\n      })\r\n      .filter(Boolean);\r\n  };\r\n\r\n  const handleSecondaryCategoryChange = (selectedOptions) => {\r\n    const selectedIds = selectedOptions.map((option) => option.value);\r\n    formik.setFieldValue(\"secondary_categories\", selectedIds);\r\n  };\r\n\r\n  const handleEdit = (educationArticles) => {\r\n    setEditingEducation(educationArticles);\r\n\r\n    formik.setValues({\r\n      primary_category_id: educationArticles?.primary_category?.id || \"\",\r\n      title: educationArticles?.title || \"\",\r\n      summary: educationArticles?.summary || \"\",\r\n      content: educationArticles?.content || \"\",\r\n      secondary_categories: educationArticles?.secondary_categories?.map(cat => String(cat.id)) || [],\r\n      feature_image: educationArticles?.feature_image || null,\r\n      transaction_scope_summary: educationArticles?.transaction_scope_summary || \"\",\r\n      trade_scope_summary: educationArticles?.trade_scope_summary || \"\",\r\n      portfolio_scope_summary: educationArticles?.portfolio_scope_summary || \"\",\r\n    });\r\n\r\n  };\r\n\r\n  const formik = useFormik({\r\n    initialValues: {\r\n      // url_path: \"\",\r\n      secondary_categories: [],\r\n      primary_category_id: \"\",\r\n      title: \"\",\r\n      feature_image: \"\",\r\n      content: \"\",\r\n      summary: \"\",\r\n      transaction_scope_summary: \"\",\r\n      trade_scope_summary: \"\",\r\n      portfolio_scope_summary: \"\"\r\n    },\r\n    validationSchema: ArticleSchema,\r\n    validateOnChange: true,\r\n    validateOnBlur: true,\r\n    onSubmit: async (values, { resetForm }) => {\r\n      setIsSubmitting(true); // disable the button\r\n    \r\n      try {\r\n        const formData = new FormData();\r\n    \r\n        if (values.primary_category_id) {\r\n          formData.append(\"primary_category_id\", values.primary_category_id);\r\n        }\r\n    \r\n        values.secondary_categories.forEach((id) => {\r\n          formData.append(\"secondary_categories[]\", id);\r\n        });\r\n    \r\n        formData.append(\"title\", values.title);\r\n        formData.append(\"content\", values.content);\r\n        formData.append(\"summary\", values.summary);\r\n        formData.append(\"feature_image\", values.feature_image);\r\n        formData.append(\"type\", \"education\");\r\n    \r\n        let response;\r\n        if (editingEducation) {\r\n          formData.append(\"_method\", \"PUT\");\r\n          response = await post(`/super-admin/articles/${editingEducation?.slug}`, formData);\r\n        } else {\r\n          response = await post(\"/super-admin/articles/store\", formData);\r\n        }\r\n    \r\n        if (response?.message) {\r\n          toast.success(response.message);\r\n        }\r\n    \r\n        resetForm(); // this clears the form fields\r\n        setEditingEducation(null);\r\n        fetchAllEducationArticles();\r\n      } catch (error) {\r\n        console.error(\"Error submitting form:\", error);\r\n        toast.error(error?.response?.data?.message ?? \"Something went wrong. Please try again.\");\r\n      } finally {\r\n        setIsSubmitting(false); // re-enable the button\r\n      }\r\n    }\r\n    \r\n  });\r\n\r\n\r\n\r\n  const handleDelete = async (id) => {\r\n    if (!window.confirm(\"Are you sure you want to delete this item?\")) return;\r\n\r\n    try {\r\n      const response = await deleteRequest(`/super-admin/articles/${id}`);\r\n      if (response?.success) {\r\n        toast.success(\"Education deleted successfully\");\r\n        setListingEducationArticles((prev) => prev.filter((item) => item.id !== id));\r\n      } else {\r\n        toast.error(\"Error deleting education\");\r\n      }\r\n    } catch (error) {\r\n      toast.error(error?.response?.data?.message || \"Error deleting\");\r\n    }\r\n  };\r\n\r\n  const handleDataFromChild = (childData) => {\r\n    setNewPage(childData);\r\n  };\r\n\r\n  const handleSort = (field) => {\r\n    setSortOrder(sortField === field ? (sortOrder === \"asc\" ? \"desc\" : \"asc\") : \"asc\");\r\n    setSortField(field);\r\n  };\r\n\r\n  return (\r\n    <DashboardLayout>\r\n      <Container>\r\n        <AdminHeading heading=\"Education\" className=\"pt-4 pb-6\" centered />\r\n        <Form onSubmit={formik.handleSubmit}>\r\n\r\n          <Row>\r\n            <Col md={6}>\r\n              <Form.Group>\r\n                <Form.Label>Primary Category</Form.Label>\r\n                <Form.Select\r\n                  name=\"primary_category_id\"\r\n                  value={formik.values.primary_category_id}\r\n                  onChange={handlePrimaryCategoryChange}\r\n                >\r\n                  <option value=\"\">Select a category</option>\r\n                  {fetchCategories?.map((category) => (\r\n                    <option key={category.id} value={category.id}>\r\n                      {category.title}\r\n                    </option>\r\n                  ))}\r\n                </Form.Select>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={6}>\r\n              <Form.Group>\r\n                <Form.Label>Secondary Categories</Form.Label>\r\n                <Select\r\n                  isMulti\r\n                  name=\"secondary_categories\"\r\n                  options={getFilteredSecondaryOptions()} // ✅ Uses function to filter options\r\n                  value={getSelectedSecondaryValues()} // ✅ Uses function to format selected values\r\n                  onChange={handleSecondaryCategoryChange} // ✅ Uses function to update values\r\n                  isDisabled={!formik.values.primary_category_id} // ✅ Disable if no primary is selected\r\n                  classNamePrefix=\"select\"\r\n                  styles={{\r\n                    control: (provided) => ({\r\n                      ...provided,\r\n                      backgroundColor: \"white\",\r\n                      color: \"black\",\r\n                      borderColor: \"#ccc\",\r\n                    }),\r\n                    menu: (provided) => ({\r\n                      ...provided,\r\n                      backgroundColor: \"white\",\r\n                      color: \"black\",\r\n                    }),\r\n                    option: (provided, state) => ({\r\n                      ...provided,\r\n                      backgroundColor: state.isSelected ? \"#f0f0f0\" : \"white\",\r\n                      color: \"black\",\r\n                      \"&:hover\": {\r\n                        backgroundColor: \"#e6e6e6\",\r\n                      },\r\n                    }),\r\n                  }}\r\n                />\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={6} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Page Title</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  name=\"title\"\r\n                  value={formik.values.title}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.title && formik.errors.title}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.title}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={6} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Header Image Filename (936x410px) - Serves CDN /education/featured/</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  name=\"feature_image\"\r\n                  value={formik.values.feature_image}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.feature_image && formik.errors.feature_image}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.feature_image}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={12} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Schema: articleBody (500-600 char summary)</Form.Label>\r\n                <Form.Control\r\n                  as=\"textarea\"\r\n                  name=\"articleBody\"\r\n                  maxLength={250}\r\n                  value={formik.values.articleBody}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.articleBody && formik.errors.articleBody}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.articleBody}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={6} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Question 1</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  name=\"question1\"\r\n                  value={formik.values.question1}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.question1 && formik.errors.question1}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.question1}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={6} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Question 1 Answer</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  name=\"answer1\"\r\n                  value={formik.values.answer1}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.answer1 && formik.errors.answer1}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.answer1}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={6} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Question 2</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  name=\"question2\"\r\n                  value={formik.values.question2}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.question2 && formik.errors.question2}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.question2}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={6} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Question 2 Answer</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  name=\"answer2\"\r\n                  value={formik.values.answer2}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.answer2 && formik.errors.answer2}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.answer2}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={6} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Question 3</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  name=\"question3\"\r\n                  value={formik.values.question3}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.question3 && formik.errors.question3}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.question3}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={6} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Question 3 Answer</Form.Label>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  name=\"answer3\"\r\n                  value={formik.values.answer3}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.answer3 && formik.errors.answer3}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.answer3}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={12} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Page Summary</Form.Label>\r\n                <Form.Control\r\n                  as=\"textarea\"\r\n                  name=\"summary\"\r\n                  maxLength={250}\r\n                  value={formik.values.summary}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.summary && formik.errors.summary}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.summary}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={12} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Transaction Scope Summary</Form.Label>\r\n                <Form.Control\r\n                  as=\"textarea\"\r\n                  name=\"transaction_scope_summary\"\r\n                  value={formik.values.transaction_scope_summary}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.transaction_scope_summary && formik.errors.transaction_scope_summary}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.transaction_scope_summary}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={12} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Trade Scope Summary</Form.Label>\r\n                <Form.Control\r\n                  as=\"textarea\"\r\n                  name=\"trade_scope_summary\"\r\n                  value={formik.values.trade_scope_summary}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.trade_scope_summary && formik.errors.trade_scope_summary}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.trade_scope_summary}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={12} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Portfolio Scope Summary</Form.Label>\r\n                <Form.Control\r\n                  as=\"textarea\"\r\n                  name=\"portfolio_scope_summary\"\r\n                  value={formik.values.portfolio_scope_summary}\r\n                  onChange={formik.handleChange}\r\n                  isInvalid={formik.touched.portfolio_scope_summary && formik.errors.portfolio_scope_summary}\r\n                />\r\n                <Form.Control.Feedback type=\"invalid\" className=\"text-white\">\r\n                  {formik.errors.portfolio_scope_summary}\r\n                </Form.Control.Feedback>\r\n              </Form.Group>\r\n            </Col>\r\n            <Col md={12} className=\"mt-3\">\r\n              <Form.Group>\r\n                <Form.Label>Body Text</Form.Label>\r\n                <div className=\"editor-container\">\r\n                  <JoditEditorComponent\r\n                    value={formik.values.content}\r\n                    onChange={(newValue) =>\r\n                      formik.setFieldValue(\"content\", newValue)\r\n                    }\r\n                  />\r\n                </div>\r\n                {formik.touched.content && formik.errors.content && (\r\n                  <div className=\"text-white\">{formik.errors.content}</div>\r\n                )}\r\n              </Form.Group>\r\n            </Col>\r\n          </Row>\r\n\r\n          {/* Submit Button */}\r\n          <Button type=\"submit\" className=\"mt-4\" disabled={isSubmitting}>\r\n            {isSubmitting ? \"Submitting...\" : editingEducation ? \"Update\" : \"Submit\"}\r\n          </Button>\r\n\r\n        </Form>\r\n\r\n        {/* Search Bar */}\r\n        <div className=\"mt-4 mb-3\">\r\n          <Form.Group>\r\n            <Form.Control\r\n              type=\"text\"\r\n              placeholder=\"Search by name...\"\r\n              value={searchTerm}\r\n              onChange={handleSearch}\r\n              className=\"w-100\"\r\n              disabled={isLoadingAllData}\r\n            />\r\n          </Form.Group>\r\n          {/* Search Results Count with white text */}\r\n          {!isLoadingAllData && filteredAndSortedArticles.length > 0 && searchTerm && (\r\n            <div className=\"mt-2\" style={{ color: '#fff' }}>\r\n              Found {filteredAndSortedArticles.length} result{filteredAndSortedArticles.length !== 1 ? 's' : ''} for \"{searchTerm}\"\r\n            </div>\r\n          )}\r\n          {!isLoadingAllData && filteredAndSortedArticles.length === 0 && searchTerm && (\r\n            <div className=\"mt-2\" style={{ color: '#fff' }}>\r\n              No results found for \"{searchTerm}\"\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Loading Indicator */}\r\n        {isLoadingAllData && (\r\n          <div className=\"text-center py-4\">\r\n            <div className=\"spinner-border\" role=\"status\">\r\n              <span className=\"visually-hidden\">Loading all education articles...</span>\r\n            </div>\r\n            <div className=\"mt-2\">Loading all education articles...</div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Articles Table */}\r\n        {!isLoadingAllData && (\r\n          <div className=\"trade_manager_entrylist\">\r\n            <ListingTable\r\n              array={listingEducationArticles}\r\n              onUpdate={handleEdit}\r\n              onDelete={handleDelete}\r\n              onSort={handleSort}\r\n              sortField={sortField}\r\n              sortOrder={sortOrder}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Pagination */}\r\n        {!isLoadingAllData && meta && meta.total > 1 && (\r\n          <div className=\"d-flex justify-content-end mt-3\">\r\n            <CustomPagination\r\n              useLinks={false}\r\n              links={meta}\r\n              onDataSend={handleDataFromChild}\r\n              pageUrl={\"super-admin/education/\"}\r\n            />\r\n          </div>\r\n        )}\r\n      </Container>\r\n    </DashboardLayout>\r\n  );\r\n};\r\n\r\nexport default EducationForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;AAdA;;;;;;;;;;;;;;;AAeA,MAAM,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAAkC,KAAK;;AAE5D,MAAM,gBAAgB;IACpB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7E,6DAA6D;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sBAAsB;IACtB,MAAM,iBAAiB;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE;gBAC3B,mBAAmB,MAAM,OAAO,CAAC,UAAU,QAAQ,SAAS,IAAI,GAAG,EAAE;YACvE,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;YACd;QACF;QAEA;IACF,GAAG,EAAE;IAGL,0DAA0D;IAC1D,MAAM,4BAA4B;QAChC,oBAAoB;QACpB,IAAI;YACF,IAAI,cAAc,EAAE;YACpB,IAAI,cAAc;YAClB,IAAI,eAAe;YAEnB,MAAO,aAAc;gBACnB,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE,uCAAuC;oBAAE,MAAM;gBAAY;gBACtF,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,UAAU,QAAQ,EAAE;gBACrC,MAAM,aAAa,UAAU,cAAc,CAAC;gBAE5C,cAAc;uBAAI;uBAAgB;iBAAS;gBAE3C,gCAAgC;gBAChC,eAAe,WAAW,aAAa,KAAK;gBAC5C;YACF;YAEA,4DAA4D;YAC5D,MAAM,iBAAiB,YAAY,IAAI,CAAC,CAAC,GAAG,IAC1C,CAAC,EAAE,KAAK,IAAI,EAAE,EAAE,aAAa,CAAC,EAAE,KAAK,IAAI;YAG3C,wBAAwB;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO,EAAE;QACX,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,+CAA+C;IAC/C,MAAM,iBAAiB,CAAC,KAAK;QAC3B,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS,MAAQ,SAAS,CAAC,IAAI,EAAE,QAAQ;IAC1E;IAEA,uDAAuD;IACvD,MAAM,qBAAqB,CAAC,UAAU,YAAY,QAAQ,OAAO,EAAE,QAAQ,KAAK;QAC9E,IAAI,WAAW;eAAI;SAAS;QAE5B,uEAAuE;QACvE,IAAI,WAAW,IAAI,IAAI;YACrB,MAAM,kBAAkB,WAAW,WAAW;YAE9C,uGAAuG;YACvG,MAAM,iBAAiB,EAAE;YACzB,MAAM,eAAe,EAAE;YAEvB,SAAS,OAAO,CAAC,CAAA;gBACf,MAAM,QAAQ,QAAQ,KAAK,EAAE,iBAAiB;gBAC9C,IAAI,MAAM,UAAU,CAAC,kBAAkB;oBACrC,eAAe,IAAI,CAAC;gBACtB,OAAO,IAAI,MAAM,QAAQ,CAAC,kBAAkB;oBAC1C,aAAa,IAAI,CAAC;gBACpB;YACF;YAEA,kDAAkD;YAClD,MAAM,eAAe,CAAC,GAAG;gBACvB,MAAM,SAAS,eAAe,GAAG;gBACjC,MAAM,SAAS,eAAe,GAAG;gBACjC,OAAO,UAAU,QACb,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;YAC3B;YAEA,eAAe,IAAI,CAAC;YACpB,aAAa,IAAI,CAAC;YAElB,4EAA4E;YAC5E,WAAW;mBAAI;mBAAmB;aAAa;QACjD,OAAO;YACL,2CAA2C;YAC3C,SAAS,IAAI,CAAC,CAAC,GAAG;gBAChB,MAAM,SAAS,eAAe,GAAG;gBACjC,MAAM,SAAS,eAAe,GAAG;gBACjC,OAAO,UAAU,QACb,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;YAC3B;QACF;QAEA,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,kBAAkB,CAAC,UAAU,MAAM;QACvC,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,oBAAoB,SAAS,KAAK,CAAC,YAAY;QAErD,MAAM,aAAa,KAAK,IAAI,CAAC,SAAS,MAAM,GAAG;QAE/C,OAAO;YACL,MAAM;YACN,YAAY;gBACV,cAAc;gBACd,UAAU;gBACV,OAAO;gBACP,OAAO,kBAAkB,MAAM;gBAC/B,eAAe,OAAO,aAAa,CAAC,KAAK,EAAE,OAAO,GAAG,GAAG;gBACxD,eAAe,OAAO,IAAI,CAAC,KAAK,EAAE,OAAO,GAAG,GAAG;gBAC/C,eAAe,SAAS,MAAM;YAChC;QACF;IACF;IAEA,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,gGAAgG;IAChG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACnC,MAAM,WAAW,mBAAmB,sBAAsB,YAAY,WAAW;YACjF,6BAA6B;YAE7B,mDAAmD;YACnD,IAAI,eAAe,MAAM,WAAW;gBAClC,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAsB;QAAY;QAAW;KAAU;IAE3D,gFAAgF;IAChF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,0BAA0B,MAAM,GAAG,GAAG;YACxC,MAAM,SAAS,gBAAgB,2BAA2B,SAAS;YACnE,4BAA4B,OAAO,IAAI;YACvC,QAAQ,OAAO,UAAU;QAC3B,OAAO,IAAI,0BAA0B,MAAM,KAAK,KAAK,qBAAqB,MAAM,GAAG,GAAG;YACpF,oBAAoB;YACpB,4BAA4B,EAAE;YAC9B,QAAQ;gBACN,cAAc;gBACd,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,eAAe;gBACf,eAAe;YACjB;QACF;IACF,GAAG;QAAC;QAA2B;KAAQ;IAEvC,MAAM,eAAe,CAAC;QACpB,cAAc,MAAM,MAAM,CAAC,KAAK;IAClC;IAEA,MAAM,8BAA8B,CAAC;QACnC,MAAM,0BAA0B,MAAM,MAAM,CAAC,KAAK;QAClD,OAAO,aAAa,CAAC,uBAAuB;QAE5C,IAAI,yBAAyB;YAC3B,MAAM,6BAA6B,OAAO,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAC1E,CAAC,KAAO,OAAO,QAAQ,OAAO;YAEhC,OAAO,aAAa,CAAC,wBAAwB;QAC/C;IACF;IAEA,MAAM,8BAA8B;QAClC,OAAO,CAAC,mBAAmB,EAAE,EAC1B,MAAM,CAAC,CAAC,MAAQ,OAAO,IAAI,EAAE,MAAM,OAAO,OAAO,MAAM,CAAC,mBAAmB,GAC3E,GAAG,CAAC,CAAC,MAAQ,CAAC;gBACb,OAAO,IAAI,EAAE;gBACb,OAAO,IAAI,KAAK;YAClB,CAAC;IACL;IAEA,MAAM,6BAA6B;QACjC,OAAO,OAAO,MAAM,CAAC,oBAAoB,CACtC,GAAG,CAAC,CAAC;YACJ,MAAM,WAAW,gBAAgB,IAAI,CAAC,CAAC,MAAQ,OAAO,IAAI,EAAE,MAAM,OAAO;YACzE,IAAI,CAAC,UAAU,OAAO;YACtB,OAAO;gBAAE,OAAO,SAAS,EAAE;gBAAE,OAAO,SAAS,KAAK;YAAC;QACrD,GACC,MAAM,CAAC;IACZ;IAEA,MAAM,gCAAgC,CAAC;QACrC,MAAM,cAAc,gBAAgB,GAAG,CAAC,CAAC,SAAW,OAAO,KAAK;QAChE,OAAO,aAAa,CAAC,wBAAwB;IAC/C;IAEA,MAAM,aAAa,CAAC;QAClB,oBAAoB;QAEpB,OAAO,SAAS,CAAC;YACf,qBAAqB,mBAAmB,kBAAkB,MAAM;YAChE,OAAO,mBAAmB,SAAS;YACnC,SAAS,mBAAmB,WAAW;YACvC,SAAS,mBAAmB,WAAW;YACvC,sBAAsB,mBAAmB,sBAAsB,IAAI,CAAA,MAAO,OAAO,IAAI,EAAE,MAAM,EAAE;YAC/F,eAAe,mBAAmB,iBAAiB;YACnD,2BAA2B,mBAAmB,6BAA6B;YAC3E,qBAAqB,mBAAmB,uBAAuB;YAC/D,yBAAyB,mBAAmB,2BAA2B;QACzE;IAEF;IAEA,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD,EAAE;QACvB,eAAe;YACb,gBAAgB;YAChB,sBAAsB,EAAE;YACxB,qBAAqB;YACrB,OAAO;YACP,eAAe;YACf,SAAS;YACT,SAAS;YACT,2BAA2B;YAC3B,qBAAqB;YACrB,yBAAyB;QAC3B;QACA,kBAAkB,4HAAA,CAAA,gBAAa;QAC/B,kBAAkB;QAClB,gBAAgB;QAChB,UAAU,OAAO,QAAQ,EAAE,SAAS,EAAE;YACpC,gBAAgB,OAAO,qBAAqB;YAE5C,IAAI;gBACF,MAAM,WAAW,IAAI;gBAErB,IAAI,OAAO,mBAAmB,EAAE;oBAC9B,SAAS,MAAM,CAAC,uBAAuB,OAAO,mBAAmB;gBACnE;gBAEA,OAAO,oBAAoB,CAAC,OAAO,CAAC,CAAC;oBACnC,SAAS,MAAM,CAAC,0BAA0B;gBAC5C;gBAEA,SAAS,MAAM,CAAC,SAAS,OAAO,KAAK;gBACrC,SAAS,MAAM,CAAC,WAAW,OAAO,OAAO;gBACzC,SAAS,MAAM,CAAC,WAAW,OAAO,OAAO;gBACzC,SAAS,MAAM,CAAC,iBAAiB,OAAO,aAAa;gBACrD,SAAS,MAAM,CAAC,QAAQ;gBAExB,IAAI;gBACJ,IAAI,kBAAkB;oBACpB,SAAS,MAAM,CAAC,WAAW;oBAC3B,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,OAAI,AAAD,EAAE,CAAC,sBAAsB,EAAE,kBAAkB,MAAM,EAAE;gBAC3E,OAAO;oBACL,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,OAAI,AAAD,EAAE,+BAA+B;gBACvD;gBAEA,IAAI,UAAU,SAAS;oBACrB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS,OAAO;gBAChC;gBAEA,aAAa,8BAA8B;gBAC3C,oBAAoB;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,OAAO,UAAU,MAAM,WAAW;YAChD,SAAU;gBACR,gBAAgB,QAAQ,uBAAuB;YACjD;QACF;IAEF;IAIA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,OAAO,OAAO,CAAC,+CAA+C;QAEnE,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,gBAAa,AAAD,EAAE,CAAC,sBAAsB,EAAE,IAAI;YAClE,IAAI,UAAU,SAAS;gBACrB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,4BAA4B,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;YAC1E,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,OAAO,UAAU,MAAM,WAAW;QAChD;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,WAAW;IACb;IAEA,MAAM,aAAa,CAAC;QAClB,aAAa,cAAc,QAAS,cAAc,QAAQ,SAAS,QAAS;QAC5E,aAAa;IACf;IAEA,qBACE,8OAAC,iIAAA,CAAA,UAAe;kBACd,cAAA,8OAAC,8LAAA,CAAA,YAAS;;8BACR,8OAAC,yJAAA,CAAA,UAAY;oBAAC,SAAQ;oBAAY,WAAU;oBAAY,QAAQ;;;;;;8BAChE,8OAAC,oLAAA,CAAA,OAAI;oBAAC,UAAU,OAAO,YAAY;;sCAEjC,8OAAC,kLAAA,CAAA,MAAG;;8CACF,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;8CACP,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,MAAM;gDACV,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,mBAAmB;gDACxC,UAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,iBAAiB,IAAI,CAAC,yBACrB,8OAAC;4DAAyB,OAAO,SAAS,EAAE;sEACzC,SAAS,KAAK;2DADJ,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;8CAOhC,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;8CACP,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC;gDACC,OAAO;gDACP,MAAK;gDACL,SAAS;gDACT,OAAO;gDACP,UAAU;gDACV,YAAY,CAAC,OAAO,MAAM,CAAC,mBAAmB;gDAC9C,iBAAgB;gDAChB,QAAQ;oDACN,SAAS,CAAC,WAAa,CAAC;4DACtB,GAAG,QAAQ;4DACX,iBAAiB;4DACjB,OAAO;4DACP,aAAa;wDACf,CAAC;oDACD,MAAM,CAAC,WAAa,CAAC;4DACnB,GAAG,QAAQ;4DACX,iBAAiB;4DACjB,OAAO;wDACT,CAAC;oDACD,QAAQ,CAAC,UAAU,QAAU,CAAC;4DAC5B,GAAG,QAAQ;4DACX,iBAAiB,MAAM,UAAU,GAAG,YAAY;4DAChD,OAAO;4DACP,WAAW;gEACT,iBAAiB;4DACnB;wDACF,CAAC;gDACH;;;;;;;;;;;;;;;;;8CAIN,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,WAAU;8CACpB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,KAAK;gDAC1B,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,MAAM,CAAC,KAAK;;;;;;0DAExD,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;8CAI1B,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,WAAU;8CACpB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,aAAa;gDAClC,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,aAAa,IAAI,OAAO,MAAM,CAAC,aAAa;;;;;;0DAExE,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,aAAa;;;;;;;;;;;;;;;;;8CAIlC,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,WAAU;8CACrB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,IAAG;gDACH,MAAK;gDACL,WAAW;gDACX,OAAO,OAAO,MAAM,CAAC,WAAW;gDAChC,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,WAAW,IAAI,OAAO,MAAM,CAAC,WAAW;;;;;;0DAEpE,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,WAAW;;;;;;;;;;;;;;;;;8CAIhC,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,WAAU;8CACpB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,SAAS;gDAC9B,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,SAAS,IAAI,OAAO,MAAM,CAAC,SAAS;;;;;;0DAEhE,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,SAAS;;;;;;;;;;;;;;;;;8CAI9B,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,WAAU;8CACpB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,OAAO;gDAC5B,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO;;;;;;0DAE5D,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;8CAI5B,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,WAAU;8CACpB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,SAAS;gDAC9B,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,SAAS,IAAI,OAAO,MAAM,CAAC,SAAS;;;;;;0DAEhE,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,SAAS;;;;;;;;;;;;;;;;;8CAI9B,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,WAAU;8CACpB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,OAAO;gDAC5B,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO;;;;;;0DAE5D,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;8CAI5B,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,WAAU;8CACpB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,SAAS;gDAC9B,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,SAAS,IAAI,OAAO,MAAM,CAAC,SAAS;;;;;;0DAEhE,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,SAAS;;;;;;;;;;;;;;;;;8CAI9B,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAG,WAAU;8CACpB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,OAAO;gDAC5B,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO;;;;;;0DAE5D,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;8CAI5B,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,WAAU;8CACrB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,IAAG;gDACH,MAAK;gDACL,WAAW;gDACX,OAAO,OAAO,MAAM,CAAC,OAAO;gDAC5B,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO;;;;;;0DAE5D,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;8CAI5B,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,WAAU;8CACrB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,IAAG;gDACH,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,yBAAyB;gDAC9C,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,yBAAyB,IAAI,OAAO,MAAM,CAAC,yBAAyB;;;;;;0DAEhG,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,yBAAyB;;;;;;;;;;;;;;;;;8CAI9C,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,WAAU;8CACrB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,IAAG;gDACH,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,mBAAmB;gDACxC,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,mBAAmB,IAAI,OAAO,MAAM,CAAC,mBAAmB;;;;;;0DAEpF,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,mBAAmB;;;;;;;;;;;;;;;;;8CAIxC,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,WAAU;8CACrB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gDACX,IAAG;gDACH,MAAK;gDACL,OAAO,OAAO,MAAM,CAAC,uBAAuB;gDAC5C,UAAU,OAAO,YAAY;gDAC7B,WAAW,OAAO,OAAO,CAAC,uBAAuB,IAAI,OAAO,MAAM,CAAC,uBAAuB;;;;;;0DAE5F,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO,CAAC,QAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAC7C,OAAO,MAAM,CAAC,uBAAuB;;;;;;;;;;;;;;;;;8CAI5C,8OAAC,kLAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,WAAU;8CACrB,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;;0DACT,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;0DAAC;;;;;;0DACZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gJAAA,CAAA,UAAoB;oDACnB,OAAO,OAAO,MAAM,CAAC,OAAO;oDAC5B,UAAU,CAAC,WACT,OAAO,aAAa,CAAC,WAAW;;;;;;;;;;;4CAIrC,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,kBAC9C,8OAAC;gDAAI,WAAU;0DAAc,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;sCAO1D,8OAAC,wLAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,WAAU;4BAAO,UAAU;sCAC9C,eAAe,kBAAkB,mBAAmB,WAAW;;;;;;;;;;;;8BAMpE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oLAAA,CAAA,OAAI,CAAC,KAAK;sCACT,cAAA,8OAAC,oLAAA,CAAA,OAAI,CAAC,OAAO;gCACX,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,WAAU;gCACV,UAAU;;;;;;;;;;;wBAIb,CAAC,oBAAoB,0BAA0B,MAAM,GAAG,KAAK,4BAC5D,8OAAC;4BAAI,WAAU;4BAAO,OAAO;gCAAE,OAAO;4BAAO;;gCAAG;gCACvC,0BAA0B,MAAM;gCAAC;gCAAQ,0BAA0B,MAAM,KAAK,IAAI,MAAM;gCAAG;gCAAO;gCAAW;;;;;;;wBAGvH,CAAC,oBAAoB,0BAA0B,MAAM,KAAK,KAAK,4BAC9D,8OAAC;4BAAI,WAAU;4BAAO,OAAO;gCAAE,OAAO;4BAAO;;gCAAG;gCACvB;gCAAW;;;;;;;;;;;;;gBAMvC,kCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAiB,MAAK;sCACnC,cAAA,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;;;;;;sCAEpC,8OAAC;4BAAI,WAAU;sCAAO;;;;;;;;;;;;gBAKzB,CAAC,kCACA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,wIAAA,CAAA,UAAY;wBACX,OAAO;wBACP,UAAU;wBACV,UAAU;wBACV,QAAQ;wBACR,WAAW;wBACX,WAAW;;;;;;;;;;;gBAMhB,CAAC,oBAAoB,QAAQ,KAAK,KAAK,GAAG,mBACzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,2IAAA,CAAA,UAAgB;wBACf,UAAU;wBACV,OAAO;wBACP,YAAY;wBACZ,SAAS;;;;;;;;;;;;;;;;;;;;;;AAOvB;uCAEe", "debugId": null}}]}